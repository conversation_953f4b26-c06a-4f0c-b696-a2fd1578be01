package com.intactfc.bloom;

import org.owasp.esapi.ESAPI;
import org.owasp.esapi.Logger;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

@SpringBootApplication
public class BloomApiApplication {

    private static final Logger log = ESAPI.getLogger(BloomApiApplication.class);


    public static void main(String[] args) {
        log.info(Logger.EVENT_SUCCESS, "APPLICATION START");
        SpringApplication.run(BloomApiApplication.class, args);
        log.info(Logger.EVENT_SUCCESS, "APPLICATION STARTED SUCCESSFULLY");
    }

}
