package com.intactfc.bloom.converter;

import org.apache.commons.lang3.StringUtils;
import org.springframework.core.convert.converter.Converter;
import org.springframework.stereotype.Component;

import intact.bloom.api.client.model.PhoneDTO;

@Component
public class PhoneDTOToStringConverter implements Converter<PhoneDTO, String> {

    @Override
    public String convert(PhoneDTO phone) {
        return (new StringBuilder())
                .append(phone.getAreaCode() != null
                        ? phone.getAreaCode()
                        : StringUtils.EMPTY)
                .append(phone.getAreaCode() != null && phone.getPhoneNumber() != null
                        ? " "
                        : StringUtils.EMPTY)
                .append(phone.getPhoneNumber() != null && phone.getPhoneNumber().length() >= 3
                        ? new StringBuffer(phone.getPhoneNumber()).insert(3, "-").toString()
                        : StringUtils.EMPTY)
                .toString();
    }

}
