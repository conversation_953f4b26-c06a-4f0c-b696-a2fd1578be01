package com.intactfc.bloom.facade;

import com.intactfc.bloom.domain.SearchClientResultDTO;
import com.intactfc.bloom.converter.PhoneDTOToStringConverter;
import com.intactfc.bloom.converter.QuoteDTOToClientDTOConverter;
import com.intactfc.bloom.converter.QuoteDTOToQuoteUIDTOConverter;
import com.intactfc.bloom.domain.QuoteUIDTO;
import com.intactfc.bloom.domain.SearchQuoteResult;
import com.intactfc.bloom.mock.MockClientDTO;
import com.intactfc.bloom.mock.quote.MockPartyDTO;
import com.intactfc.bloom.mock.quote.MockQuoteDTO;
import com.intactfc.bloom.mock.quote.MockQuoteUIDTO;
import com.intactfc.bloom.service.BloomRepositoryClient;
import intact.bloom.api.client.api.ApiException;
import intact.bloom.api.client.model.PartyDTO;
import intact.bloom.api.client.model.QuoteDTO;
import intact.bloom.api.client.model.QuoteDTO.WorkFlowStatusEnum;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.reset;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class CallBackAgentFacadeTest {

    @Mock
    private QuoteDTOToQuoteUIDTOConverter converter;
    
    @Mock
    private PhoneDTOToStringConverter phoneConverter;
    
    @Mock
    private QuoteDTOToClientDTOConverter quoteToClientConverter;

    @Mock
    private BloomRepositoryClient mockBloomRepositoryClient;

    @InjectMocks
    private CallBackAgentFacade callBackAgentFacade = new CallBackAgentFacade();

    private ClientDTO clientDTO;

    @Test
    void test_searchQuotes_by_workFlowStatus_return_the_quotes_list() throws ApiException {

        List<QuoteDTO> quoteDTOs = new ArrayList<>();
        QuoteDTO quote1 = MockQuoteDTO.createQuoteDTO();
        quoteDTOs.add(quote1);

        when(mockBloomRepositoryClient.searchQuotesByWorkFlowStatus(anyString())).thenReturn(quoteDTOs);

        List<QuoteDTO> quotes = callBackAgentFacade.searchQuotesByWorkFlowStatus("test");

        assertEquals(1,quotes.size());
    }

    @Test
    void test_search_all_related_quote_return_searchQuoteResult() throws ApiException {

        QuoteUIDTO quoteUIDTO = MockQuoteUIDTO.createQuoteUIDTO();

        List<QuoteDTO> QuoteDTOLinkedList = new LinkedList<>();
        List<QuoteDTO> QuoteDTOOtherList = new LinkedList<>();

        QuoteDTO quoteDTO = MockQuoteDTO.createQuoteDTO();
        quoteDTO.setWorkFlowStatus(QuoteDTO.WorkFlowStatusEnum.SENT_DIALER);
        quoteDTO.setConfirmationNumber("123456");

        QuoteDTO quoteDTO2 = MockQuoteDTO.createQuoteDTO();
        quoteDTO2.setWorkFlowStatus(QuoteDTO.WorkFlowStatusEnum.LINKED);
        QuoteDTO quoteDTO3 = MockQuoteDTO.createQuoteDTO();
        quoteDTO3.setWorkFlowStatus(QuoteDTO.WorkFlowStatusEnum.LINKED);
        QuoteDTOLinkedList.add(quoteDTO2);
        QuoteDTOLinkedList.add(quoteDTO3);

        QuoteDTO quoteDTO4 = MockQuoteDTO.createQuoteDTO();
        quoteDTO4.setWorkFlowStatus(QuoteDTO.WorkFlowStatusEnum.DELETED);
        QuoteDTO quoteDTO5 = MockQuoteDTO.createQuoteDTO();
        quoteDTO5.setWorkFlowStatus(QuoteDTO.WorkFlowStatusEnum.DELETED);
        QuoteDTOOtherList.add(quoteDTO4);
        QuoteDTOOtherList.add(quoteDTO5);

        List<QuoteDTO> listAllRelatedQuote = new LinkedList<>();
        listAllRelatedQuote.addAll(QuoteDTOLinkedList);
        listAllRelatedQuote.addAll(QuoteDTOOtherList);

        when(mockBloomRepositoryClient.searchQuotesByQuoteNumber(anyString())).thenReturn(quoteDTO);
        when(mockBloomRepositoryClient.searchQuotesByConfirmationNumber(anyString())).thenReturn(listAllRelatedQuote);
        when(converter.convert(quoteDTO)).thenReturn(quoteUIDTO);

        SearchQuoteResult searchQuoteResult = callBackAgentFacade.searchAllRelatedToQuoteNumber("1234");

        assertEquals(1,searchQuoteResult.getMainQuotes().size());
        assertEquals("12",searchQuoteResult.getMainQuotes().getFirst().getId());
        assertEquals(2,searchQuoteResult.getOtherQuotes().size());
        assertEquals(2,searchQuoteResult.getLinkedQuotes().size());


    }

    @Test
    void test_searchByQuoteNumber_also_return_ready_quotes() throws ApiException {
        QuoteDTO quoteDTO = MockQuoteDTO.createQuoteDTO();
        quoteDTO.setConfirmationNumber(null);

        when(mockBloomRepositoryClient.searchQuotesByQuoteNumber(anyString())).thenReturn(quoteDTO);

        SearchQuoteResult searchQuoteResult = callBackAgentFacade.searchAllRelatedToQuoteNumber("1234");

        assertEquals(1,searchQuoteResult.getMainQuotes().size());
        assertEquals(0,searchQuoteResult.getOtherQuotes().size());
        assertEquals(0,searchQuoteResult.getLinkedQuotes().size());
        verify(mockBloomRepositoryClient,times(0)).searchQuotesByConfirmationNumber(anyString());
        verify(converter,times(1)).convert(any(QuoteDTO.class));

        reset(mockBloomRepositoryClient,converter);

        quoteDTO.setConfirmationNumber("");
        when(mockBloomRepositoryClient.searchQuotesByQuoteNumber(anyString())).thenReturn(quoteDTO);

        searchQuoteResult = callBackAgentFacade.searchAllRelatedToQuoteNumber("1234");

        assertEquals(1,searchQuoteResult.getMainQuotes().size());
        assertEquals(0,searchQuoteResult.getOtherQuotes().size());
        assertEquals(0,searchQuoteResult.getLinkedQuotes().size());
        verify(mockBloomRepositoryClient,times(0)).searchQuotesByConfirmationNumber(anyString());
        verify(converter).convert(any(QuoteDTO.class));
    }

    @Test
    void searchQuotesByPhoneNumber() throws ApiException {
        List<QuoteDTO> quotes = new ArrayList<>();
        QuoteDTO mainQuote1 = MockQuoteDTO.createQuoteDTO();
        mainQuote1.setWorkFlowStatus(WorkFlowStatusEnum.SENT_DIALER);
        mainQuote1.setConfirmationNumber("see13421");
        quotes.add(mainQuote1);

        QuoteDTO linkedQuote1 = MockQuoteDTO.createQuoteDTO();
        linkedQuote1.setWorkFlowStatus(WorkFlowStatusEnum.LINKED);
        linkedQuote1.setConfirmationNumber("see13421");
        quotes.add(linkedQuote1);

        QuoteDTO otherQuote1 = MockQuoteDTO.createQuoteDTO();
        otherQuote1.setWorkFlowStatus(WorkFlowStatusEnum.DELETED);
        otherQuote1.setConfirmationNumber("see13421");
        quotes.add(otherQuote1);

        when(mockBloomRepositoryClient.searchQuotesByPhone(anyString())).thenReturn(quotes);

        SearchQuoteResult result = callBackAgentFacade.searchQuotesByPhoneNumber("************");

        assertEquals(1,result.getMainQuotes().size());
        assertEquals(1,result.getLinkedQuotes().size());
        assertEquals(1,result.getOtherQuotes().size());
        verify(mockBloomRepositoryClient).searchQuotesByPhone(eq("5144567894"));
    }

    @Test
    void searchClientsByName() throws ApiException {
        List<QuoteDTO> quotes = new ArrayList<>();
        QuoteDTO client1Quote1 = MockQuoteDTO.createQuoteDTO();
        client1Quote1.setConfirmationNumber("see13421");
        quotes.add(client1Quote1);

        QuoteDTO client1Quote2 = MockQuoteDTO.createQuoteDTO();
        client1Quote2.setConfirmationNumber("see13421");
        quotes.add(client1Quote2);

        QuoteDTO client2Quote1 = MockQuoteDTO.createQuoteDTO();
        client2Quote1.getParty().companyName("Test Company");
        client2Quote1.getParty().setPartyType(PartyDTO.PartyTypeEnum.COMPANY);
        client2Quote1.setConfirmationNumber("see13421");
        quotes.add(client2Quote1);

        QuoteDTO quote3 = MockQuoteDTO.createQuoteDTO();
        quote3.getParty().setFirstName("Sir NotInResults");
        quotes.add(quote3);

        ClientDTO expectedClient1 = MockClientDTO.createClientDTO();
        ClientDTO expectedClient2 = MockClientDTO.createClientDTO();
        expectedClient2.setName("Test Company");

        when(mockBloomRepositoryClient.quotesTextSearch(anyString())).thenReturn(quotes);
        when(quoteToClientConverter.convert(client1Quote1)).thenReturn(expectedClient1);
        when(quoteToClientConverter.convert(client1Quote2)).thenReturn(expectedClient1);
        when(quoteToClientConverter.convert(client2Quote1)).thenReturn(expectedClient2);


        SearchClientResultDTO result = callBackAgentFacade.searchClientsByName("Test");
        assertEquals(3,result.getClients().size());
        assertTrue(result.getClients().contains(expectedClient1));
        assertTrue(result.getClients().contains(expectedClient2));
    }

    @Test
    void searchQuotesByClient() throws ApiException {
        List<QuoteDTO> quotes = new ArrayList<>();

        PartyDTO crocodileDundee = MockPartyDTO.createPartyDTO();
        crocodileDundee.setFirstName("Crocodile");
        crocodileDundee.setLastName("Dundee");

        PartyDTO bruceWayne = MockPartyDTO.createPartyDTO();
        bruceWayne.setFirstName("Bruce");
        bruceWayne.setLastName("Wayne");

        QuoteDTO mainQuote1 = MockQuoteDTO.createQuoteDTO();
        mainQuote1.setParty(crocodileDundee);
        mainQuote1.setWorkFlowStatus(WorkFlowStatusEnum.SENT_DIALER);
        mainQuote1.setConfirmationNumber("see13421");
        quotes.add(mainQuote1);

        QuoteDTO mainQuote2 = MockQuoteDTO.createQuoteDTO();
        mainQuote2.setParty(bruceWayne);
        mainQuote2.setWorkFlowStatus(WorkFlowStatusEnum.SENT_DIALER);
        mainQuote2.setConfirmationNumber("see13421");
        quotes.add(mainQuote2);

        QuoteDTO linkedQuote1 = MockQuoteDTO.createQuoteDTO();
        linkedQuote1.setParty(crocodileDundee);
        linkedQuote1.setWorkFlowStatus(WorkFlowStatusEnum.LINKED);
        linkedQuote1.setConfirmationNumber("see13421");
        quotes.add(linkedQuote1);

        QuoteDTO otherQuote1 = MockQuoteDTO.createQuoteDTO();
        otherQuote1.setParty(crocodileDundee);
        otherQuote1.setWorkFlowStatus(WorkFlowStatusEnum.DELETED);
        otherQuote1.setConfirmationNumber("see13421");
        quotes.add(otherQuote1);

        // mimic the search by phone number
        when(mockBloomRepositoryClient.searchQuotesByPhone(anyString())).thenReturn(quotes);

        clientDTO = new ClientDTO();

        clientDTO.setFirstName("Crocodile");
        clientDTO.setLastName("Dundee");
        clientDTO.setPhone("************");
        clientDTO.setPostalCode("H1H1H1");

        SearchQuoteResult result = callBackAgentFacade.searchQuotesByClient(clientDTO);

        assertEquals(1,result.getMainQuotes().size());
        assertEquals(1,result.getLinkedQuotes().size());
        assertEquals(1,result.getOtherQuotes().size());
        verify(mockBloomRepositoryClient).searchQuotesByPhone(eq("5144567894"));
    }
}
