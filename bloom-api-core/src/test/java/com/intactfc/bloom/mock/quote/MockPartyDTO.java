package com.intactfc.bloom.mock.quote;

import intact.bloom.api.client.model.AddressDTO;
import intact.bloom.api.client.model.PartyDTO;

public class MockPartyDTO {

    public static PartyDTO createPartyDTO() {
        PartyDTO partyDTO= new PartyDTO();

        partyDTO.setPartyType(PartyDTO.PartyTypeEnum.PERSON);
        partyDTO.setCompanyOwnerInd(false);
        partyDTO.setDriverInd(true);
        partyDTO.setFirstName("Test");
        partyDTO.setLastName("Test");
        partyDTO.setPolicyHolderInd(true);
        partyDTO.setCompanyName("COMPAGNY");
        
        AddressDTO address = new AddressDTO();
        address.setPostalCode("H1H1H1");
        address.setProvince("QC");
        partyDTO.setAddress(address);
        return partyDTO;
    }
}
