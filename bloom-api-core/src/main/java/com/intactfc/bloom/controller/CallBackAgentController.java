package com.intactfc.bloom.controller;

import com.intactfc.bloom.domain.SearchClientResultDTO;
import com.intactfc.bloom.domain.SearchQuoteResult;
import com.intactfc.bloom.facade.CallBackAgentFacade;
import com.intactfc.bloom.facade.ClientDTO;
import intact.bloom.api.client.model.QuoteDTO;
import org.apache.commons.lang3.StringUtils;
import java.util.List;

import org.owasp.esapi.ESAPI;
import org.owasp.esapi.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@CrossOrigin(origins = "*", maxAge = 3600)
@RequestMapping("/cba")
public class CallBackAgentController {

    private static final Logger log = ESAPI.getLogger(CallBackAgentController.class);

    @Autowired
    CallBackAgentFacade callBackAgentFacade;
    
    @GetMapping("/workFlowStatus/{workFlowStatus}")
    public ResponseEntity<Object> searchQuotesByWorkFlowStatus(@PathVariable String workFlowStatus){

        List<QuoteDTO> listQuoteDTO = this.callBackAgentFacade.searchQuotesByWorkFlowStatus(workFlowStatus);

        if(CollectionUtils.isEmpty(listQuoteDTO)){
            return new ResponseEntity<>(HttpStatus.NO_CONTENT);
        }

        return new ResponseEntity<>(listQuoteDTO, HttpStatus.OK);
    }

    @GetMapping(value="/quotes", params= {"quoteNumber"})
    public ResponseEntity<Object> searchQuoteByQuoteNumber(@RequestParam String quoteNumber){

        if(StringUtils.isBlank(quoteNumber)){
            return new ResponseEntity<>("quoteNumber can't be empty", HttpStatus.BAD_REQUEST);
        }

        log.info(Logger.EVENT_SUCCESS, "Searching quote related to [quoteNumber:%s]".formatted(quoteNumber));

        SearchQuoteResult searchQuoteResult = this.callBackAgentFacade.searchAllRelatedToQuoteNumber(quoteNumber.trim());

        log.info(Logger.EVENT_SUCCESS, "Quote search successful related to [quoteNumber:%s]".formatted(quoteNumber));

        return new ResponseEntity<>(searchQuoteResult, HttpStatus.OK);
    }

    
    @GetMapping(value="/quotes", params= {"phoneNumber"})
    public ResponseEntity<SearchQuoteResult> searchQuotesByPhoneNumber(@RequestParam String phoneNumber) {

        log.info(Logger.EVENT_SUCCESS, "Searching quote related to [phoneNumber:%s]".formatted(phoneNumber));

        SearchQuoteResult result = this.callBackAgentFacade.searchQuotesByPhoneNumber(phoneNumber);

        log.info(Logger.EVENT_SUCCESS, "Quote search successful related to [phoneNumber:%s]".formatted(phoneNumber));

        return new ResponseEntity<>(result, HttpStatus.OK);
    }
    
    @GetMapping(value="/clients")
    public ResponseEntity<SearchClientResultDTO> searchClientsByName(@RequestParam String name) {

        log.info(Logger.EVENT_SUCCESS, "Searching quote [name:%s]".formatted(name));

        SearchClientResultDTO result = this.callBackAgentFacade.searchClientsByName(name);

        log.info(Logger.EVENT_SUCCESS, "Successfully found quote with [name:%s]".formatted(name));

        return new ResponseEntity<>(result, HttpStatus.OK);
    }

    @PostMapping(value="/quotes/client")
    public ResponseEntity<SearchQuoteResult> searchQuotesByClient(@RequestBody ClientDTO clientDTO) {
        SearchQuoteResult result = this.callBackAgentFacade.searchQuotesByClient(clientDTO);
        return new ResponseEntity<>(result, HttpStatus.OK);
    }
    
}
