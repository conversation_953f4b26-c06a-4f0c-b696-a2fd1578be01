package com.intactfc.bloom.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.intactfc.bloom.domain.DialerDTO;
import com.intactfc.bloom.domain.DialerResponseDTO;
import com.intactfc.bloom.domain.PageUIDTO;
import com.intactfc.bloom.domain.QuoteUIDTO;
import com.intactfc.bloom.exceptions.DialerServiceException;
import com.intactfc.bloom.exceptions.handlers.CustomRestExceptionHandler;
import com.intactfc.bloom.facade.DialerFacade;
import com.intactfc.bloom.facade.DialerFileCreatorFacade;
import com.intactfc.bloom.mock.quote.MockQuoteUIDTO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.isNull;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.put;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@ExtendWith(MockitoExtension.class)
public class DialerFileCreatorControllerTest {

    @InjectMocks
    private DialerFileCreatorController dialerFileCreatorController = new DialerFileCreatorController();

    @Mock
    private DialerFacade dialerFacade;

    @Mock
    private DialerFileCreatorFacade dialerFileCreatorFacade;

    private MockMvc mockMvc;

    private DialerResponseDTO dialerResponseDTO;

    private DialerDTO dialerDTO;

    @BeforeEach
    void setUp() {
        dialerResponseDTO = new DialerResponseDTO();
        dialerResponseDTO.setDialerConfirmationNumber("12345");
        mockMvc = MockMvcBuilders.standaloneSetup(dialerFileCreatorController)
                .setControllerAdvice(new CustomRestExceptionHandler())
                .build();
        dialerDTO = DialerDTO.builder().uuid("34t5C622-6670-448D-9354-3FE93fF22F4O").linkedQuoteNumbers(new String[]{"*************-9999-9999-************","QT100000"}).deletedQuoteNumbers(null).build();

    }

    @Test
    void test_send_success() throws Exception {

        Mockito.when(dialerFacade.sendToDialer(any())).thenReturn(dialerResponseDTO);

        mockMvc.perform(post("/quotes/dialer")
                .content(asJsonString(dialerDTO))
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @Test
    void test_send_with_linkquotes() throws Exception {

        dialerResponseDTO.setDialerConfirmationNumber(null);
        dialerResponseDTO.setNewLinkedQuotesUIDTO(Arrays.asList(new QuoteUIDTO[]{MockQuoteUIDTO.createQuoteUIDTO(),MockQuoteUIDTO.createQuoteUIDTO()}));
        Mockito.when(dialerFacade.sendToDialer(any())).thenReturn(dialerResponseDTO);

        mockMvc.perform(post("/quotes/dialer")
                .content(asJsonString(dialerDTO))
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isConflict());
    }

    @Test
    void test_send_throw_DialerServiceException() throws Exception {

        Mockito.when(dialerFacade.sendToDialer(any())).thenThrow(new DialerServiceException("frfr",HttpStatus.INTERNAL_SERVER_ERROR.value()));

        mockMvc.perform(post("/quotes/dialer")
                .content(asJsonString(dialerDTO))
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isInternalServerError());
    }

    @Test
    void test_searchQuote_by_phoneNumber_with_null_quoteUIDTOs_return_not_bad_request_status() throws Exception {
        mockMvc.perform(get("/quotes/search-by-phonenumber?phoneNumber=")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isBadRequest());
    }


    @Test
    void test_searchQuote_by_phoneNumber_with_valid_phoneNumber_return_OK_status() throws Exception {

        List<QuoteUIDTO> quoteUIDTOs = new ArrayList<>();
        quoteUIDTOs.add(MockQuoteUIDTO.createQuoteUIDTO());

        Mockito.when(dialerFileCreatorFacade.getQuotesForReview(anyString())).thenReturn(quoteUIDTOs);

        mockMvc.perform(get("/quotes/search-by-phonenumber?phoneNumber=514-2022020")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }


    @Test
    void test_partialUpdateQuote_success() throws Exception {

        Mockito.when(dialerFileCreatorFacade.partialUpdateQuote(any(QuoteUIDTO.class))).thenReturn(MockQuoteUIDTO.createQuoteUIDTO());

        mockMvc.perform(put("/quotes")
                .contentType(MediaType.APPLICATION_JSON)
                .content(asJsonString(MockQuoteUIDTO.createQuoteUIDTO())))
                .andExpect(status().isOk());
    }

    @Test
    void test_updateQuote_null_quoteUIDTO_id_attribute_should_return_internalServerError() throws Exception {
        QuoteUIDTO quoteUIDTO = null;

        mockMvc.perform(put("/quotes")
                .contentType(MediaType.APPLICATION_JSON)
                .content(asJsonString(quoteUIDTO)))
                .andExpect(status().isInternalServerError());
    }


    @Test
    void test_leadList_should_return_ok() throws Exception {
        PageUIDTO pageDto = null;

        when(dialerFileCreatorFacade.searchLeadListQuotes(any(String.class),any(String.class),isNull(),any(Integer.class),any(Integer.class))).thenReturn(pageDto);

        mockMvc.perform(get("/quotes/leadList?creationDateFrom=0&creationDateTo=1000&page=0&size=100")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @Test
    void test_leadList_with_province_should_return_ok() throws Exception {
        PageUIDTO pageDto = null;

        when(dialerFileCreatorFacade.searchLeadListQuotes(any(String.class),any(String.class),any(String.class),any(Integer.class),any(Integer.class))).thenReturn(pageDto);

        mockMvc.perform(get("/quotes/leadList?creationDateFrom=0&province=QC&creationDateTo=1000&page=0&size=100")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    public static String asJsonString(final Object obj) {
        try {
            return new ObjectMapper().writeValueAsString(obj);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

}
