package com.intactfc.bloom.utils;

import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.joda.time.Minutes;
import org.joda.time.format.DateTimeFormat;


public class DateUtils {

    private DateUtils() {}

    public static String removeMinutesToDateTime(DateTime dateTime, String minutes) {
        return dateTime.minusMinutes(Integer.parseInt(minutes)).toString();

    }

    public static String getMinutesBetweenTwoDateTimes(DateTime dateTimeFrom, DateTime dateTimeTo) {
        if(dateTimeFrom == null || dateTimeTo == null) {
            return null;
        }

        return String.valueOf(Minutes.minutesBetween(dateTimeFrom, dateTimeTo).getMinutes());
    }

    public static String formatDateTimeToString(DateTime dateTime, String pattern) {

        if(dateTime == null
            || StringUtils.isBlank(pattern)) {
            return null;
        }

        return dateTime.toString(DateTimeFormat.forPattern(pattern));

    }

}