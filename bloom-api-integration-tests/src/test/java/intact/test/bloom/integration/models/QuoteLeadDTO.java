package intact.test.bloom.integration.models;

import java.util.List;
import lombok.Data;

@Data
public class QuoteLeadDTO {
  private String id;
  private String name;
  private String phoneNumber;
  private String date;
  private AddressDTO address;
  private Boolean isHidden;
  private Boolean isQuoteIncomplete;
  private Boolean isQuoteComplete;
  private Boolean isQuoteRoadBlock;
  private Boolean isQuoteAlReadyClient;
  private Boolean isQuoteToBeReviewed;
  private String workflowStatus;
  private String creationDateTime;
  private List<VehicleDTO> vehicles;
  private String quoteNumber;
  private BrokerDTO broker;
}
