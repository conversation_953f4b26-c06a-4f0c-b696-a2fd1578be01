package com.intactfc.bloom.mock.quotedialer;

import intact.bloom.dialer.service.client.model.QuoteDialerDTO;
import intact.bloom.dialer.service.client.model.VehicleDTO;

import java.util.ArrayList;
import java.util.List;

public class MockQuoteDialerDTO {

    public static QuoteDialerDTO createQuoteDialerDTO() {


        List listChildQuotes = new ArrayList();
        listChildQuotes.add("QF95123647");
        listChildQuotes.add("IR85231444");

        QuoteDialerDTO quoteDialerDTO = new QuoteDialerDTO();
        quoteDialerDTO.setQuoteSource(QuoteDialerDTO.QuoteSourceEnum.QI);
        quoteDialerDTO.setQuoteNumber("QF123456798");
//        quoteDialerDTO.setCreationTimeStamp(DateTime.now());
        quoteDialerDTO.setDistributorNumber("64178");
        quoteDialerDTO.setId("12");
        quoteDialerDTO.setQuoteAppEnum(QuoteDialerDTO.QuoteAppEnumEnum.AUTOQUOTE);
        quoteDialerDTO.setLineofBusiness(QuoteDialerDTO.LineofBusinessEnum.PERSONAL);
        quoteDialerDTO.setSourceUnderwritingCompany(QuoteDialerDTO.SourceUnderwritingCompanyEnum.WESTERN);
        quoteDialerDTO.setParty(MockPartyDTO.createPartyDTO());
        quoteDialerDTO.setPhone(MockPhoneDTO.createPhoneDTO());
        quoteDialerDTO.setDatasourceOrigin(QuoteDialerDTO.DatasourceOriginEnum.PLP);
        quoteDialerDTO.getParty().setProvince("QC");
        quoteDialerDTO.setListRelatedQuotes(listChildQuotes);
        quoteDialerDTO.setListVehiclesDTO(getListVehiclesDTO());

        return quoteDialerDTO;

    }

    public static  List<VehicleDTO> getListVehiclesDTO() {
        List<VehicleDTO> vehicleDTOList = new ArrayList();
        VehicleDTO vehicleDTO = new VehicleDTO();

        vehicleDTO.setMake("toyota");
        vehicleDTO.setModel("sienna");
        vehicleDTO.setYear("2019");


        vehicleDTOList.add(vehicleDTO);

        return vehicleDTOList;
    }
}
