FROM docker-group.iad.ca.inet:8473/intact/ocp-openjdk21-base:1@sha256:0492b9434e78fd271d12d44fa0b753b103f8d7d9e97c54d9512fc2490c92cbb8

ARG LABEL_VERSION
LABEL version=$LABEL_VERSION

LABEL OWNER_TEAM="SE_INTACT_LAB" \
      TRIBE_TEAM="acquisition" \
      SQUAD_TEAM="AEGIS"

USER root

RUN groupadd --gid 5000 bloom \
      && useradd --home-dir /home/<USER>/bin/sh --skel /dev/null bloom

ENV JAVA_OPTS="-XX:+UseG1GC -Xmx1g -Xms256m -XX:+UnlockExperimentalVMOptions -XX:MaxGCPauseMillis=50 -XX:G1NewSizePercent=8 -Duser.timezone=America/Montreal --add-opens java.base/java.io=ALL-UNNAMED"

USER bloom

COPY bloom-api-core/target/bloom-api-core.jar /opt/bloom-api-core.jar
COPY entrypoint.sh /entrypoint.sh

EXPOSE 8080

WORKDIR "/opt"

ENTRYPOINT ["/entrypoint.sh"]
