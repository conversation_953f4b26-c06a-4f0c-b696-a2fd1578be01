package com.intactfc.bloom.converter;

import intact.bloom.api.client.model.PhoneDTO;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertEquals;

class PhoneDTOTOStringConverterTest {
    
    private PhoneDTOToStringConverter converter = new PhoneDTOToStringConverter();

    @Test
    void convert_wiht_area_code_and_phone_number() {
        PhoneDTO phone = this.createBasicEntryPhone("514","9999999");
        String result = converter.convert(phone);
        assertEquals("************",result);
    }

    @Test
    void convert_with_null_area_code() {
        PhoneDTO phone = this.createBasicEntryPhone(null,"9999999");
        String result = converter.convert(phone);
        assertEquals("999-9999",result);
    }

    @Test
    void convert_with_null_phone_number() {
        PhoneDTO phone = this.createBasicEntryPhone("514",null);
        String result = converter.convert(phone);
        assertEquals("514",result);
    }
    
    private PhoneDTO createBasicEntryPhone(String areaCode, String phoneNumber) {
        PhoneDTO basicPhone = new PhoneDTO();
        basicPhone.setAreaCode(areaCode);
        basicPhone.setPhoneNumber(phoneNumber);
        return basicPhone;
    }
    
}
