package com.intactfc.bloom.domain;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.NotBlank;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DialerDTO {

    @NotBlank(message = "uuid cannot be null or empty")
    private String uuid;
    private String[] deletedQuoteNumbers;
    private String[] linkedQuoteNumbers;
}
