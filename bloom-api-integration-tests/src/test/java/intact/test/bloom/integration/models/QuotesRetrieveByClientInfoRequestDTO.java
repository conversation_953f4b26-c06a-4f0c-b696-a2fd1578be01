package intact.test.bloom.integration.models;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class QuotesRetrieveByClientInfoRequestDTO {
  private String name;
  private String firstName;
  private String lastName;
  private String companyName;
  private String phone;
  private String province;
  private String postalCode;

}
