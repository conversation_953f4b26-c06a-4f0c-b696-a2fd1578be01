package com.intact.dialer.config;

import intact.bloom.dialer.service.client.api.ApiClient;
import intact.bloom.dialer.service.client.api.DialerControllerApi;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.PropertySource;

@Configuration
@PropertySource("classpath:dialer-service.properties")
@ComponentScan(basePackages = "com.intact.dialer.service")
public class DialerConfiguration {

    @Value("${dialer.service.url}")
    private String dialerServiceUrl;

    @Value("${broker-dialer-service.api.key}")
    private String dialerApiKeyValue;

  @Bean
  DialerControllerApi dialerControllerApi() {

    DialerControllerApi dialerControllerApi = new DialerControllerApi();
    dialerControllerApi.setApiClient(apiClient());

    return dialerControllerApi;
  }

    public ApiClient apiClient() {
        ApiClient apiClient = new ApiClient();
        apiClient.setBasePath(this.dialerServiceUrl);
        apiClient.setApiKey(dialerApiKeyValue);
        return apiClient;
    }
}
