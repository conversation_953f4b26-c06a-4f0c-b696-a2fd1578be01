openapi: 3.0.1
info:
  title: Dialer Service Project
  description: Dialer Service
  contact:
    name: SpiderWeb Team Intact
    url: https://intactlab.atlassian.net/wiki/spaces/SW/overview
    email: <EMAIL>
  version: 0.0.1
servers:
  - url: https://bloom.intact.net/v1/dialer
security:
  - dialerApiKey: []
tags:
  - name: dialer-controller
    description: Dialer Service
paths:
  /:
    get:
      tags:
        - dialer-controller
      summary: insertBrokerToDatabase
      operationId: insertBrokerToDatabaseUsingGET
      responses:
        200:
          description: OK
          content:
            '*/*':
              schema:
                type: object
        401:
          description: Unauthorized
          content: {}
        403:
          description: Forbidden
          content: {}
        404:
          description: Not Found
          content: {}
      deprecated: false
  /match:
    post:
      tags:
        - dialer-controller
      summary: matchDialer
      operationId: matchDialerUsingPOST
      requestBody:
        description: quoteDialerDTO
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/QuoteDialerDTO'
        required: true
      responses:
        200:
          description: OK
          content:
            '*/*':
              schema:
                type: object
        201:
          description: Created
          content: {}
        401:
          description: Unauthorized
          content: {}
        403:
          description: Forbidden
          content: {}
        404:
          description: Not Found
          content: {}
      deprecated: false
      x-codegen-request-body-name: quoteDialerDTO
  /send:
    post:
      tags:
        - dialer-controller
      summary: createDialerFile
      operationId: createDialerFileUsingPOST
      requestBody:
        description: quoteDialerDTO
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/QuoteDialerDTO'
        required: true
      responses:
        200:
          description: OK
          content:
            '*/*':
              schema:
                type: string
        201:
          description: Created
          content: {}
        401:
          description: Unauthorized
          content: {}
        403:
          description: Forbidden
          content: {}
        404:
          description: Not Found
          content: {}
      deprecated: false
      x-codegen-request-body-name: quoteDialerDTO
components:
  schemas:
    ErrorDTO:
      type: object
      properties:
        code:
          type: string
        description:
          type: string
    PartyDTO:
      type: object
      properties:
        firstName:
          type: string
        lastName:
          type: string
        companyName:
          type: string
        province:
          type: string
    PhoneDTO:
      type: object
      properties:
        areaCode:
          type: string
        extension:
          type: string
        phoneNumber:
          type: string
    QuoteDialerDTO:
      required:
        - creationTimeStamp
        - distributorNumber
        - quoteNumber
      type: object
      properties:
        id:
          type: string
        creationTimeStamp:
          type: string
          format: date-time
        inceptionDate:
          type: string
          format: date
        distributorNumber:
          type: string
        lineofBusiness:
          type: string
          enum:
            - PERSONAL
            - COMMERCIAL
        quoteNumber:
          type: string
        quoteSource:
          type: string
          enum:
            - QI
            - QB
            - QIB
        quoteStatus:
          type: string
          enum:
            - ALREADY_CLIENT
            - BIND_INCOMPLETE
            - QUOTE_COMPLETE
            - QUOTE_INCOMPLETE
            - ROAD_BLOCK
            - IN_PROGRESS_READY_TO_BIND
            - IN_FORCE
        userName:
          type: string
        listRelatedQuotes:
          type: array
          properties:
            length:
              type: integer
          items:
            type: string
        datasourceOrigin:
          type: string
          enum:
            - PLP
            - XPAS
            - XPAS_CL
            - IPAS
        sourceUnderwritingCompany:
          type: string
          enum:
            - INTACT_CENTRAL_ATLANTIC
            - INTACT_QUEBEC
            - INTACT_WESTERN
        phone:
          $ref: '#/components/schemas/PhoneDTO'
        listVehiclesDTO:
          type: array
          properties:
            length:
              type: integer
          items:
            $ref: "#/components/schemas/VehicleDTO"
        party:
          $ref: '#/components/schemas/PartyDTO'
        quoteAppEnum:
          type: string
          enum:
            - AUTOQUOTE
            - AUTO_QUICKQUOTE
            - HOME_QUICKQUOTE
            - IRCA_QUICKQUOTE
            - BUNDLE_QUOTE
            - BUNDLE_QUOTE_AUTO
            - BUNDLE_QUOTE_HOME
            - COMMERCIAL_QUICKQUOTE
    VehicleDTO:
      type: object
      properties:
        make:
          type: string
        model:
          type: string
        year:
          type: string
    ListErrorDTO:
      type: array
      properties:
        length:
          type: integer
      items:
        $ref: '#/components/schemas/ErrorDTO'
  securitySchemes:
    dialerApiKey:
      type: apiKey
      name: dialerApiKey
      in: header
