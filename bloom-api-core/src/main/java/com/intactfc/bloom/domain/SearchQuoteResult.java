package com.intactfc.bloom.domain;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SearchQuoteResult {
    @Builder.Default
    private List<QuoteUIDTO> mainQuotes = new ArrayList<>();
    @Builder.Default
    private List<QuoteUIDTO> linkedQuotes = new ArrayList<>();
    @Builder.Default
    private List<QuoteUIDTO> otherQuotes = new ArrayList<>();
}
