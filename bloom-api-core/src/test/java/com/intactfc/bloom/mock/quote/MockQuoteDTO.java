package com.intactfc.bloom.mock.quote;

import intact.bloom.api.client.model.BrokerDTO;
import intact.bloom.api.client.model.QuoteDTO;
import intact.bloom.api.client.model.VehicleDTO;
import org.joda.time.DateTime;
import org.joda.time.LocalDateTime;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class MockQuoteDTO {


    public  static QuoteDTO createQuoteDTO() {
        QuoteDTO quoteDTO  = new QuoteDTO();
        quoteDTO.setQuoteNumber("QF123456798");
        quoteDTO.setCreationTimeStamp(getDateTime());
        quoteDTO.setBroker(getBrokerDTO());
        quoteDTO.setQuoteSource(QuoteDTO.QuoteSourceEnum.QI);
        quoteDTO.setId("34t5C622-6670-448D-9354-3FE93fF22F4O");
        quoteDTO.setQuoteAppEnum(QuoteDTO.QuoteAppEnumEnum.HOME_QUICKQUOTE);
        quoteDTO.setLineofBusiness(QuoteDTO.LineofBusinessEnum.PERSONAL);
        quoteDTO.setLastUpdateTimeStamp(LocalDateTime.now().plusDays(1).toDateTime());
        quoteDTO.setSourceUnderwritingCompany(QuoteDTO.SourceUnderwritingCompanyEnum.QUEBEC);
        quoteDTO.setWorkFlowStatus(QuoteDTO.WorkFlowStatusEnum.READY);
        quoteDTO.setParty(MockPartyDTO.createPartyDTO());
        quoteDTO.setPhone(MockPhoneDTO.createPhoneDTO());
        quoteDTO.setListVehiclesDTO(getListVehiclesDTO());
        quoteDTO.setQuoteStatus(QuoteDTO.QuoteStatusEnum.QUOTE_COMPLETE);
        quoteDTO.mostRecentInd(true);
        quoteDTO.relatedInd(false);
        quoteDTO.setDatasourceOrigin(QuoteDTO.DatasourceOriginEnum.PLP);

        return quoteDTO;
    }

    public static  List<VehicleDTO> getListVehiclesDTO() {
        List<VehicleDTO> vehicleDTOList = new ArrayList<>();
        VehicleDTO vehicleDTO = new VehicleDTO();

        vehicleDTO.setCode("123456");
        vehicleDTO.setMake("toyota");
        vehicleDTO.setModel("sienna");
        vehicleDTO.setYear("2019");


        vehicleDTOList.add(vehicleDTO);

        return vehicleDTOList;
    }

    private static DateTime getDateTime() {
        Date date = new Date();

        DateTime dateTime = new DateTime(date.toInstant().toString());

        return dateTime;
    }

    private static BrokerDTO getBrokerDTO() {

        BrokerDTO brokerDTO = new BrokerDTO();
        brokerDTO.setNumber("1234");
        brokerDTO.setName("BOW VALLEY INSURANCE SERVICES");
        brokerDTO.setPhoneNumber("4032979400");
        brokerDTO.setEmail("<EMAIL>");

        return brokerDTO;
    }
}
