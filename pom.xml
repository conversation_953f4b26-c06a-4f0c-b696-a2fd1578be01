<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>intact.web.bloom</groupId>
    <artifactId>bloom-api</artifactId>
    <version>3.0.94-SNAPSHOT</version>
    <packaging>pom</packaging>

    <name>bloom-api</name>
    <description>REST API project for the Broker Leads Office and Operations Management (Bloom) app</description>

    <modules>
        <module>bloom-api-core</module>
        <module>dialer-service-client</module>
        <module>bloom-api-integration-tests</module>
    </modules>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <jdk.version>21</jdk.version>
        <jdk.vendor>temurin</jdk.vendor>
        <sonar.coverage.exclusions>**/*DTO.*,**/*Application.*,**/*Config.*</sonar.coverage.exclusions>
        <mavenJacocoPluginVersion>0.8.13</mavenJacocoPluginVersion>
        <spring-boot.version>3.5.5</spring-boot.version>
        <spring-cloud.version>2023.0.1</spring-cloud.version>

        <!-- Default setup for releasing project's target/site in Nexus -->
        <site-url-protocol>https:/</site-url-protocol>
        <site-url-root>/prod-nexus-b2eapp.iad.ca.inet:8443/nexus/content/sites/doc/</site-url-root>
        <distributionManagement-site-url-protocol>dav:</distributionManagement-site-url-protocol>

        <!-- dont touch the followings if you dont want to break anything -->
        <site-url>
            ${site-url-protocol}${site-url-root}${project.groupId}/${project.artifactId}/${project.version}/${project.artifactId}
        </site-url>
        <distributionManagement-site-url>${distributionManagement-site-url-protocol}${site-url}
        </distributionManagement-site-url>

        <swagger-annotations-version>1.6.16</swagger-annotations-version>
        <jackson-version>2.16.0</jackson-version>
        <jackson-databind-version>2.16.0</jackson-databind-version>
        <jersey-version>3.1.11</jersey-version>
        <jodatime-version>2.12.5</jodatime-version>
        <junit-version>4.12</junit-version>
        <maven.compiler.source>21</maven.compiler.source>
        <maven.compiler.target>21</maven.compiler.target>
    </properties>

    <scm>
        <connection>scm:git:https://githubifc.iad.ca.inet/lab-se/bloom-api.git</connection>
        <developerConnection>scm:git:https://githubifc.iad.ca.inet/lab-se/bloom-api.git</developerConnection>
        <url>https://githubifc.iad.ca.inet/lab-se/bloom-api</url>
        <tag>HEAD</tag>
    </scm>


    <distributionManagement>
        <repository>
            <id>nexus</id>
            <name>intact releases repository</name>
            <url>
                https://prod-nexus-b2eapp.iad.ca.inet:8443/nexus/content/repositories/releases/
            </url>
        </repository>
        <snapshotRepository>
            <id>nexus</id>
            <name>intact snapshots repository</name>
            <url>
                https://prod-nexus-b2eapp.iad.ca.inet:8443/nexus/content/repositories/snapshots/
            </url>
        </snapshotRepository>
    </distributionManagement>


    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- Fixing CVE-2023-2976 -->
            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>32.1.3-jre</version>
            </dependency>

            <dependency>
                <groupId>org.owasp.antisamy</groupId>
                <artifactId>antisamy</artifactId>
                <version>1.7.8</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <plugins>
            <!-- Jacoco Maven Plugin -->
            <plugin>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
                <version>${mavenJacocoPluginVersion}</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>prepare-agent</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>report</id>
                        <phase>test</phase>
                        <goals>
                            <goal>report</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.14.0</version>
                <configuration>
                    <compilerArgs>
                        <arg>-parameters</arg>
                    </compilerArgs>
                    <release>${jdk.version}</release>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>versions-maven-plugin</artifactId>
                <version>2.16.2</version>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-toolchains-plugin</artifactId>
                <version>3.1.0</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>toolchain</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <toolchains>
                        <jdk>
                            <version>${jdk.version}</version>
                            <vendor>${jdk.vendor}</vendor>
                        </jdk>
                    </toolchains>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>3.2.3</version>
            </plugin>

        </plugins>
    </build>

</project>
