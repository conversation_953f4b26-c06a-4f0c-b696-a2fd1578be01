package intact.test.bloom.integration.glue;

import intact.test.bloom.integration.context.TestContext;
import intact.test.integration.functions.QAFunctions;
import intact.test.integration.glue.steps.AbstractSteps;
import intact.test.integration.models.acquisition.webquote.offer.OfferRequestDTO;
import io.cucumber.java.en.Given;

public class SetupSteps extends AbstractSteps<TestContext> {

  public SetupSteps(TestContext context) {
    super(context);
  }

  @Given("the user's full name contains random string")
  public void theUsersFullNameContainsRandomString() {

    String first = QAFunctions.randomName(15);
    String last = "Test";

    context.getNextRequestDTO(OfferRequestDTO.class).getFirstParty().setFirstName(first).setLastName(last);
  }


}
