package com.intactfc.bloom.controller;


import com.intactfc.bloom.domain.UserAccount;
import com.intactfc.bloom.domain.UserUIDTO;
import org.owasp.esapi.ESAPI;
import org.owasp.esapi.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@CrossOrigin(origins = "*", maxAge = 3600)
@RestController
@RequestMapping("/user")
public class UserAccountController {

    private static final Logger log = ESAPI.getLogger(UserAccountController.class);

    @Autowired
    UserAccount userAccount;

    @GetMapping
    public ResponseEntity<UserUIDTO> getUser(){
        UserUIDTO userUIDTO = new UserUIDTO(userAccount.getUserId());

        log.info(Logger.EVENT_SUCCESS, "The userUIDTO is %s".formatted(userUIDTO));

        return new ResponseEntity<>(userUIDTO, HttpStatus.OK);
    }
}
