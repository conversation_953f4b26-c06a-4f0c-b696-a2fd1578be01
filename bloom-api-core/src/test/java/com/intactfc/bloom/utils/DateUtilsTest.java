package com.intactfc.bloom.utils;

import org.joda.time.DateTime;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;

import java.util.Calendar;

import static org.junit.jupiter.api.Assertions.*;

@Disabled
class DateUtilsTest {

    @Test
    void test_remove_5_MinutesToDateTime() {
        DateTime dateTime = new DateTime(2019,Calendar.JUNE,12,12,25,20);
        String dateTimeString = DateUtils.removeMinutesToDateTime(dateTime,"5");

        assertNotNull(dateTime);
        assertEquals("2019-05-12T12:20:20.000-04:00",dateTimeString);
    }


    @Test
    void test_getMinutesBetweenTwoDateTimes_success() {
        DateTime today = DateTime.now();
        DateTime yesterday = today.minusDays(1);

        String dateTimeString = DateUtils.getMinutesBetweenTwoDateTimes(yesterday,today);

        assertNotNull(dateTimeString);
        assertEquals("1440",dateTimeString);
    }

    @Test
    void test_getMinutesBetweenTwoDateTimes_DatimeFrom_null() {
        DateTime today = DateTime.now();
        DateTime yesterday = null;

        String dateTimeString = DateUtils.getMinutesBetweenTwoDateTimes(yesterday,today);

        assertNull(dateTimeString);
    }

    @Test
    void test_getMinutesBetweenTwoDateTimes_null_dateTimeFrom_null_dateTimeTo_null() {
        DateTime today = null;
        DateTime yesterday = null;

        String dateTimeString = DateUtils.getMinutesBetweenTwoDateTimes(yesterday,today);

        assertNull(dateTimeString);
    }

    @Test
    void test_formatDateTimeToString_succes() {

        DateTime dateTime = new DateTime(2019,Calendar.JUNE,12,12,25,20);
        String DATE_TIME_FORMAT = "yyyy/MM/dd - HH'h'mm";

        String dateTimeToString = DateUtils.formatDateTimeToString(dateTime,DATE_TIME_FORMAT);

        assertEquals("2019/05/12 - 12h25",dateTimeToString);
    }

    @Test
    void test_formatDateTimeToString_with_null_dateTime_should_return_null() {

        String DATE_TIME_FORMAT = "yyyy/MM/dd - HH'h'mm";

        String dateTimeToString = DateUtils.formatDateTimeToString(null,DATE_TIME_FORMAT);

        assertNull(dateTimeToString);
    }

    @Test
    void test_formatDateTimeToString_with_null_pattern_should_return_null() {

        DateTime dateTime = new DateTime(2019,Calendar.JUNE,12,12,25,20);

        String dateTimeToString = DateUtils.formatDateTimeToString(dateTime,null);

        assertNull(dateTimeToString);
    }

}