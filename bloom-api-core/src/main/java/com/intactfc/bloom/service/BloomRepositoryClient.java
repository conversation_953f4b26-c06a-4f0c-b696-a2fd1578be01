package com.intactfc.bloom.service;

import intact.bloom.api.client.api.ApiException;
import intact.bloom.api.client.api.BloomRepositoryServiceApi;
import intact.bloom.api.client.model.Page;
import intact.bloom.api.client.model.QuoteDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;

@Component
public class BloomRepositoryClient {

    @Autowired
    private BloomRepositoryServiceApi bloomRepositoryServiceApi;

    public List<QuoteDTO> searchQuotes(String creationDateFrom, String creationDateTo, String id, String phoneNumber,String[] quoteNumbers) throws ApiException {
        return this.bloomRepositoryServiceApi.searchQuotes(creationDateFrom, creationDateTo, id, phoneNumber, quoteNumbers!= null ?  Arrays.asList(quoteNumbers) : null);
    }

    public QuoteDTO updateQuote(QuoteDTO quote) throws ApiException {
        return this.bloomRepositoryServiceApi.upsert(quote);
    }

    public QuoteDTO partialUpdateQuote(QuoteDTO quote) throws ApiException {
        return this.bloomRepositoryServiceApi.partialUpdateQuote(quote);
    }

    public QuoteDTO searchQuotesByQuoteNumber(String quoteNumber) throws ApiException {
        return this.bloomRepositoryServiceApi.searchQuotesByQuoteNumber(quoteNumber);
    }

    public List<QuoteDTO> searchQuotesByWorkFlowStatus(String workFlowStatus) throws ApiException {
        return this.bloomRepositoryServiceApi.searchQuotesByWorkFlowStatus(workFlowStatus);
    }

    public List<QuoteDTO> searchQuotesByConfirmationNumber(String confirmationNumber) throws ApiException {
        return this.bloomRepositoryServiceApi.searchQuotesByConfirmationNumber(confirmationNumber);
    }
    
    public List<QuoteDTO> quotesTextSearch(String text) throws ApiException {
        return this.bloomRepositoryServiceApi.quotesFullTextSearch(text);
    }
    
    public Page searchLeadListQuotes(String creationDateFrom, String creationDateTo, Integer page, Integer size) throws ApiException {
        return this.searchLeadListQuotes(creationDateFrom, creationDateTo, null, page, size);
    }
    
    public Page searchLeadListQuotes(String creationDateFrom, String creationDateTo, String province, Integer page, Integer size) throws ApiException {
        return this.bloomRepositoryServiceApi.searchLeadListQuotes(creationDateFrom, creationDateTo, page, size, province);
    }

    public QuoteDTO searchQuoteByUuid(String uuid)  throws ApiException {
        return  this.bloomRepositoryServiceApi.searchQuoteByUuid(uuid);
    }

    public List<QuoteDTO> searchQuotesByPhone(String phone)  throws ApiException {
        return  this.bloomRepositoryServiceApi.searchQuoteByPhone(phone);
    }

}
