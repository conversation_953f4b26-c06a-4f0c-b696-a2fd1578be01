package intact.test.bloom.integration.glue;

import intact.test.integration.glue.steps.AbstractRequestSteps;
import intact.test.bloom.integration.context.TestContext;
import intact.test.bloom.integration.api.BloomAPI;
import intact.test.integration.models.acquisition.webquote.offer.OfferResponseDTO;
import intact.test.integration.models.acquisition.webquote.offer.models.party.PartyDTO;
import io.cucumber.java.en.When;
import org.apache.logging.log4j.LogManager;

public class RequestSteps extends AbstractRequestSteps<TestContext> {

  private final BloomAPI bloomAPI;

  public RequestSteps(TestContext context) {
    super(context);
    this.bloomAPI = new BloomAPI(context);
  }

  @When("the user retrieves list of leads 30 min to 2 months")
  public void theUserRetrievesListOfLeads() {
    String creationDateFrom = "30"; // 30 min
    String creationDateTo = "86400"; // 2 months in minutes
    String page = "0";
    String size = "100";
    bloomAPI.quotesLeadListRequest(creationDateFrom, creationDateTo, page, size).execute();
  }

  @When("the user attempts to retrieve list of leads missing required query param")
  public void theUserAttemptsToRetrieveListOfLeadsMissingRequiredQueryParam() {
    String creationDateFrom = "30"; // 30 min
    String creationDateTo = "86400"; // 2 months in minutes
    String page = "0";
    String size = "";
    bloomAPI.quotesLeadListRequest(creationDateFrom, creationDateTo, page, size).execute();
  }

  @When("the user retrieves new cba quote by quote number")
  public void theUserRetrievesCbaQuoteByQuoteNumber() {
    waitForUpload();
    String quoteNumber = context.getLastResponseOfType(OfferResponseDTO.class).getAutoQuote()
        .getNumber();
    bloomAPI.cbaQuotesRetrieveByQuoteNumberRequest(quoteNumber).execute();
  }

  @When("the user retrieves new cba quote by phone number")
  public void theUserRetrievesCbaQuotesByPhoneNumber() {
    waitForUpload();
    PartyDTO partyDTO = context.getLastResponseOfType(OfferResponseDTO.class).getFirstParty();
    String phoneNumber = partyDTO.getPhoneNumbers().getFirst().getAreaCode() +
        partyDTO.getPhoneNumbers().getFirst().getPhoneNumber();
    bloomAPI.cbaQuotesRetrieveByPhoneNumberRequest(phoneNumber).execute();
  }

  @When("the user retrieves new cba quote by client info")
  public void theUserRetrievesCbaQuotesByClientInfo() {
    waitForUpload();
    PartyDTO partyDTO = context.getLastResponseOfType(OfferResponseDTO.class).getFirstParty();
    String firstName = partyDTO.getFirstName();
    String lastName = partyDTO.getLastName();
    String phone =
        partyDTO.getPhoneNumbers().getFirst().getAreaCode() + partyDTO.getPhoneNumbers().getFirst()
            .getPhoneNumber();
    String province = partyDTO.getAddress().getProvinceState();
    String postalCode = partyDTO.getAddress().getPostalCode();

    bloomAPI.cbaQuotesRetrieveByClientInfoRequest(firstName, lastName, phone, province, postalCode)
        .execute();
  }


  @When("the user retrieves cba clients by name")
  public void theUserRetrievesCbaClientsByName() {
    waitForUpload();
    PartyDTO partyDTO = context.getLastResponseOfType(OfferResponseDTO.class).getFirstParty();
    String firstName = partyDTO.getFirstName();
    String lastName = partyDTO.getLastName();
    bloomAPI.cbaClientsRetrieveByName(firstName + " " + lastName).execute();
  }


  @When("the user sends to dialer quote number")
  public void theUserSendsQuoteToDialer() {
    waitForUpload();
    String quoteNumber = context.getLastResponseOfType(OfferResponseDTO.class).getWebQuoteId();
    bloomAPI.quotesDialerRequest(quoteNumber).execute();
  }

  @When("the user retrieves quotes by phone number")
  public void theUserRetrievesQuotesByPhoneNumber() {
    waitForUpload();
    PartyDTO partyDTO = context.getLastResponseOfType(OfferResponseDTO.class).getFirstParty();
    String phoneNumber = partyDTO.getPhoneNumbers().getFirst().getAreaCode() +
        partyDTO.getPhoneNumbers().getFirst().getPhoneNumber();
    bloomAPI.quotesSearchByPhoneNumberRequest(phoneNumber).execute();
  }


  private void waitForUpload() {
    try {
      Thread.sleep(10000);
    } catch (InterruptedException i) {
      LogManager.getLogger(RequestSteps.class.getName()).error("Error while waiting for upload");
    }
  }
}
