package com.intactfc.bloom.facade;

import com.intactfc.bloom.converter.PageToPageUIDTOConverter;
import com.intactfc.bloom.converter.QuoteDTOToQuoteUIDTOConverter;
import com.intactfc.bloom.domain.PageUIDTO;
import com.intactfc.bloom.domain.QuoteUIDTO;
import com.intactfc.bloom.domain.UserAccount;
import com.intactfc.bloom.exceptions.BloomRepositoryServiceException;
import com.intactfc.bloom.mock.quote.MockQuoteDTO;
import com.intactfc.bloom.mock.quote.MockQuoteUIDTO;
import com.intactfc.bloom.mock.quotedialer.MockQuoteDialerDTO;
import com.intactfc.bloom.service.BloomRepositoryClient;
import intact.bloom.api.client.api.ApiException;
import intact.bloom.api.client.model.Page;
import intact.bloom.api.client.model.QuoteDTO;
import intact.bloom.dialer.service.client.model.QuoteDialerDTO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.springframework.core.convert.converter.Converter;
import org.springframework.web.client.RestTemplate;

import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class DialerFileCreatorFacadeTest {

    @Mock
    private QuoteDTOToQuoteUIDTOConverter mockConverter;
    
    @Mock
    private UserAccount userAccount;

    @Mock
    private PageToPageUIDTOConverter pageToPageUIDTOConverter;

    @Mock
    private BloomRepositoryClient mockBloomRepositoryClient;

    @InjectMocks
    private DialerFileCreatorFacade dialerFileCreatorFacade = new DialerFileCreatorFacade();

    @Mock
    private RestTemplate restTemplate;

    @Mock
    Converter<QuoteDTO, QuoteDialerDTO> quoteDTOToQuoteDialerDTOConverter;

    @BeforeEach
    public void init() {
        when(this.userAccount.getUserId()).thenReturn("TestUser");
    }

    @Test
    void test_partialUpdateQuote_withHiddenFlagValue_shouldCallBloomRepoUpdateAndReturnUpdatedQuote() throws ApiException {

        QuoteUIDTO testEntryQuote = MockQuoteUIDTO.createQuoteUIDTO();
        testEntryQuote.setIsHidden(true);

        QuoteDTO testQuoteToUpdate = new QuoteDTO();
        testQuoteToUpdate.setId("12");
        testQuoteToUpdate.setWorkFlowStatus(QuoteDTO.WorkFlowStatusEnum.DELETED);


        when(this.mockBloomRepositoryClient.partialUpdateQuote(testQuoteToUpdate)).thenReturn(MockQuoteDTO.createQuoteDTO());
        when(quoteDTOToQuoteDialerDTOConverter.convert(any(QuoteDTO.class))).thenReturn(MockQuoteDialerDTO.createQuoteDialerDTO());
        when(mockConverter.convert(any(QuoteDTO.class))).thenReturn(testEntryQuote);


        this.dialerFileCreatorFacade.partialUpdateQuote(testEntryQuote);

        verify(this.mockBloomRepositoryClient,times(1)).partialUpdateQuote(testQuoteToUpdate);
    }

    @Test
    void updateQuote_withNullEntryQuote_should_not_call_partialUpdateQuote() throws ApiException {
        assertThrows(IllegalArgumentException.class,() -> {

            this.dialerFileCreatorFacade.partialUpdateQuote(null);

            verify(this.mockBloomRepositoryClient,never()).partialUpdateQuote(null);
        });
    }

    @Test
    void test_partialUpdateQuote_throwingAPIException_shouldThrowBloomRepoServiceException() throws ApiException {
        assertThrows(BloomRepositoryServiceException.class,() -> {

            QuoteUIDTO testEntryQuote = MockQuoteUIDTO.createQuoteUIDTO();
            when(this.mockBloomRepositoryClient.partialUpdateQuote(any(QuoteDTO.class))).thenThrow(new ApiException());

            this.dialerFileCreatorFacade.partialUpdateQuote(testEntryQuote);
        });
    }

    @Test
    void test_partialUpdateQuote_withUpdateQuoteThrowingAPIException_shouldThrowBloomRepoServiceException() throws ApiException {
        assertThrows(BloomRepositoryServiceException.class,() -> {

            QuoteUIDTO testEntryQuote = MockQuoteUIDTO.createQuoteUIDTO();

            when(this.mockBloomRepositoryClient.partialUpdateQuote(any(QuoteDTO.class))).thenThrow(new ApiException());

            this.dialerFileCreatorFacade.partialUpdateQuote(testEntryQuote);
        });
    }


    @Test
    void test_searchQuotes_by_phoneNumber_should_not_filter_the_quotes_list() throws ApiException {
        List<QuoteDTO> quoteDTOs = new ArrayList<>();
        QuoteDTO quote1 = MockQuoteDTO.createQuoteDTO();
        QuoteDTO quote2 = MockQuoteDTO.createQuoteDTO();
        QuoteDTO quote3 = MockQuoteDTO.createQuoteDTO();
        QuoteDTO quote4 = MockQuoteDTO.createQuoteDTO();
        quoteDTOs.add(quote1);
        quoteDTOs.add(quote2);
        quoteDTOs.add(quote3);
        quoteDTOs.add(quote4);
        String phoneNumber = quote1.getPhone().getAreaCode() + "-" + quote1.getPhone().getPhoneNumber();
        when(mockBloomRepositoryClient.searchQuotesByPhone(phoneNumber)).thenReturn(quoteDTOs);

        List<QuoteUIDTO> quoteUIDTOs = dialerFileCreatorFacade.getQuotesForReview(phoneNumber);

        assertEquals(4,quoteUIDTOs.size());
    }

    @Test
    void test_searchQuotes_by_workFlowStatus_return_the_quotes_list() throws ApiException {

        List<QuoteDTO> quoteDTOs = new ArrayList<>();
        QuoteDTO quote1 = MockQuoteDTO.createQuoteDTO();
        quoteDTOs.add(quote1);

        when(mockBloomRepositoryClient.searchQuotesByWorkFlowStatus(anyString())).thenReturn(quoteDTOs);

        List<QuoteDTO> quotes = dialerFileCreatorFacade.searchQuotesByWorkFlowStatus("test");

        assertEquals(1,quotes.size());
    }

    @Test
    void test_searchQuotes_by_quoteNumber_return_the_quotes_list() throws ApiException {

        QuoteDTO quote1 = MockQuoteDTO.createQuoteDTO();

        when(mockBloomRepositoryClient.searchQuotesByQuoteNumber(anyString())).thenReturn(quote1);

        QuoteDTO quote = dialerFileCreatorFacade.searchByQuoteNumber("test");

        assertNotNull(quote);
    }

    @Test
    void test_searchleadList() throws ApiException {
        Page page = null;

        PageUIDTO pageUIDTO = new PageUIDTO();

        pageUIDTO.setTotalElements(10);

        when(this.mockBloomRepositoryClient.searchLeadListQuotes(any(String.class),any(String.class),isNull(),any(Integer.class),any(Integer.class))).thenReturn(page);

        when(pageToPageUIDTOConverter.convert(page)).thenReturn(pageUIDTO);

        PageUIDTO pageUIDTO1 = dialerFileCreatorFacade.searchLeadListQuotes("0","1000",null,0,10);

        assertEquals(10,pageUIDTO1.getTotalElements().longValue());
    }

    @Test
    void test_searchleadList_with_province() throws ApiException {
        Page page = null;

        PageUIDTO pageUIDTO = new PageUIDTO();

        pageUIDTO.setTotalElements(10);

        when(this.mockBloomRepositoryClient.searchLeadListQuotes(any(String.class),any(String.class),eq("QC"),any(Integer.class),any(Integer.class))).thenReturn(page);

        when(pageToPageUIDTOConverter.convert(page)).thenReturn(pageUIDTO);

        PageUIDTO pageUIDTO1 = dialerFileCreatorFacade.searchLeadListQuotes("0","1000","QC",0,10);

        assertEquals(10,pageUIDTO1.getTotalElements().longValue());
    }
    
}
