package com.intactfc.bloom.facade;

import com.intactfc.bloom.converter.QuoteDTOToClientDTOConverter;
import com.intactfc.bloom.converter.QuoteDTOToQuoteUIDTOConverter;
import com.intactfc.bloom.domain.QuoteUIDTO;
import com.intactfc.bloom.domain.SearchClientResultDTO;
import com.intactfc.bloom.domain.SearchQuoteResult;
import com.intactfc.bloom.exceptions.BloomRepositoryServiceException;
import com.intactfc.bloom.exceptions.QuoteNotFoundException;
import com.intactfc.bloom.service.BloomRepositoryClient;
import intact.bloom.api.client.api.ApiException;
import intact.bloom.api.client.model.QuoteDTO;
import intact.bloom.api.client.model.QuoteDTO.WorkFlowStatusEnum;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Component
public class CallBackAgentFacade {

    @Autowired
    private BloomRepositoryClient bloomRepositoryClient;

    @Autowired
    private QuoteDTOToQuoteUIDTOConverter quoteDTOToQuoteUIDTOConverter;
    
    @Autowired
    private QuoteDTOToClientDTOConverter quoteToClientConverter;

    public List<QuoteDTO> searchQuotesByWorkFlowStatus(String workFlowStatus) {
        List<QuoteDTO> quoteDTOs;

        try {
            quoteDTOs = bloomRepositoryClient.searchQuotesByWorkFlowStatus(workFlowStatus);
        } catch (ApiException e) {
            String msg = "Exception when calling Search for the workFlowStatus : %s".formatted(workFlowStatus);
            throw new BloomRepositoryServiceException(msg, e);
        }

        return quoteDTOs;
    }

    public SearchQuoteResult searchAllRelatedToQuoteNumber(String quoteNumber) {
        QuoteDTO quoteDTO;
        QuoteUIDTO mainQuote;
        SearchQuoteResult searchQuoteResult = new SearchQuoteResult();
        
        quoteDTO = getQuoteByQuoteNumber(quoteNumber);

        if(quoteDTO == null){
            return searchQuoteResult;
        }

        mainQuote = quoteDTOToQuoteUIDTOConverter.convert(quoteDTO);
        searchQuoteResult.getMainQuotes().add(mainQuote);
        
        List<QuoteDTO> listAllRelatedQuotes = null;
        
        if(StringUtils.isNotBlank(quoteDTO.getConfirmationNumber())){
            listAllRelatedQuotes = searchQuotesByConfirmationNumber(quoteDTO.getConfirmationNumber());
        }

        if(CollectionUtils.isNotEmpty(listAllRelatedQuotes)){
            listAllRelatedQuotes.stream().filter(quote -> quote.getWorkFlowStatus() == QuoteDTO.WorkFlowStatusEnum.LINKED)
                                         .map(quote -> quoteDTOToQuoteUIDTOConverter.convert(quote))
                                         .forEach(searchQuoteResult.getLinkedQuotes()::add);

            listAllRelatedQuotes.stream().filter(quote -> quote.getWorkFlowStatus() == QuoteDTO.WorkFlowStatusEnum.DELETED)
                                         .map(quote -> quoteDTOToQuoteUIDTOConverter.convert(quote))
                                         .forEach(searchQuoteResult.getOtherQuotes()::add);
            
            listAllRelatedQuotes.stream().filter(quote -> quote.getWorkFlowStatus() == QuoteDTO.WorkFlowStatusEnum.UPLOADED)
                                         .map(quote -> quoteDTOToQuoteUIDTOConverter.convert(quote))
                                         .forEach(searchQuoteResult.getOtherQuotes()::add);
        }

        return searchQuoteResult;
    }
    
    /**
     * Get a quote by it's unique quote number.
     * @param quoteNumber
     * @throws QuoteNotFoundException When no quote can be identified by the given quote number.
     * @return The quote identified by the specified quote number.
     */
    private QuoteDTO getQuoteByQuoteNumber(String quoteNumber) {
        QuoteDTO quoteDTO;

        try {
            quoteDTO = bloomRepositoryClient.searchQuotesByQuoteNumber(quoteNumber);
        } catch (ApiException e) {
            throw new BloomRepositoryServiceException("Exception when calling Search for the quoteNumber : %s".formatted(quoteNumber), e);
        }

        return quoteDTO;
    }

    private List<QuoteDTO> searchQuotesByConfirmationNumber(String confirmationNumber) {
        List<QuoteDTO> quoteDTOs;

        try {
            quoteDTOs = bloomRepositoryClient.searchQuotesByConfirmationNumber(confirmationNumber);

        } catch (ApiException e) {
            throw new BloomRepositoryServiceException("Exception when calling Search for the confirmationNumber : %s".formatted(confirmationNumber), e);
        }

        return quoteDTOs;
    }
    
    /**
     * Performs a 'fulltext search' for clients.  For now, only the phone number works, but eventually we could receive the name of the client.
     * @param phoneNumber
     * @return
     */
    public SearchQuoteResult searchQuotesByPhoneNumber(String phoneNumber) {
        
        if(StringUtils.isBlank(phoneNumber)) {
            return new SearchQuoteResult();
        }
        
        List<QuoteDTO> quotes = findQuotesByPhoneNumber(phoneNumber);
        
        if(quotes == null) {
            return new SearchQuoteResult();
        }
        
        return  createSearchQuoteResult(quotes);
    }

    private SearchQuoteResult createSearchQuoteResult(List<QuoteDTO> quotes) {
        SearchQuoteResult searchResult = new SearchQuoteResult();
        quotes.sort(Comparator.comparing(QuoteDTO::getCreationTimeStamp).reversed());

        List<QuoteUIDTO> mainQuotes = quotes.stream()
                .filter(quote -> WorkFlowStatusEnum.SENT_DIALER == quote.getWorkFlowStatus())
                .map(quote -> quoteDTOToQuoteUIDTOConverter.convert(quote))
                .collect(Collectors.toList());
        searchResult.setMainQuotes(mainQuotes);

        List<QuoteUIDTO> linkedQuotes = quotes.stream()
                .filter(quote -> WorkFlowStatusEnum.LINKED == quote.getWorkFlowStatus())
                .map(quote -> quoteDTOToQuoteUIDTOConverter.convert(quote))
                .collect(Collectors.toList());
        searchResult.setLinkedQuotes(linkedQuotes);

        List<QuoteUIDTO> otherQuotes = quotes.stream()
                .filter(quote -> {

                    // this can only happen with very old data,
                    // so we assume the status is ready if we have no dialer confirmation number
                    // otherwise we assume SENT_DIALER.
                    if(quote.getWorkFlowStatus() == null) {
                        if(StringUtils.isBlank(quote.getConfirmationNumber())) {
                            quote.setWorkFlowStatus(WorkFlowStatusEnum.READY);
                        } else {
                            quote.setWorkFlowStatus(WorkFlowStatusEnum.SENT_DIALER);
                        }
                    }

                    return switch (quote.getWorkFlowStatus()) {
                        case DELETED, UPLOADED, READY, NO_FWP_NEEDED -> true;
                        default -> false;
                    };
                })
                .map(quote -> quoteDTOToQuoteUIDTOConverter.convert(quote))
                .collect(Collectors.toList());
        searchResult.setOtherQuotes(otherQuotes);

        return searchResult;
    }

    private List<QuoteDTO> findQuotesByPhoneNumber(String phoneNumber) {
        String phoneNumberToSearch = phoneNumber.replaceAll("[^\\d.]", "");
        List<QuoteDTO> quotes;
        try {
            quotes= bloomRepositoryClient.searchQuotesByPhone(phoneNumberToSearch);
        } catch (ApiException e) {
            String message = "Error from bloom-repository-service while searching quotes by phone number (%s)".formatted(phoneNumberToSearch);
            throw new BloomRepositoryServiceException(message, e);
        }
        return quotes;
    }

    public SearchClientResultDTO searchClientsByName(String fullname) {
        SearchClientResultDTO result = new SearchClientResultDTO();
        List<QuoteDTO> quotes;
        try {
            quotes= bloomRepositoryClient.quotesTextSearch(fullname);
        } catch (ApiException e) {
            String message = "Error from bloom-repository-service while searching quotes by client name (%s)".formatted(fullname);
            throw new BloomRepositoryServiceException(message, e);
        }
        
        Set<ClientDTO> clients = quotes.stream()
                .map(quoteToClientConverter::convert)
                .collect(Collectors.toSet());
        
        result.setClients(new ArrayList<>(clients));
        return result;
    }

    public SearchQuoteResult searchQuotesByClient(ClientDTO clientDTO) {
        List<QuoteDTO> quotesByPhoneNumber;
        List<QuoteDTO> quotesByClient = null;

        if (clientDTO != null) {
            quotesByPhoneNumber = this.findQuotesByPhoneNumber(clientDTO.getPhone()); //Phone Number
    
            if(quotesByPhoneNumber != null) {
                quotesByClient = quotesByPhoneNumber.stream()
                        .filter(q -> StringUtils.equalsIgnoreCase(clientDTO.getFirstName(),(q.getParty().getFirstName()))
                                && StringUtils.equalsIgnoreCase(clientDTO.getLastName(), q.getParty().getLastName())
                                && StringUtils.equalsIgnoreCase(clientDTO.getPostalCode(),q.getParty().getAddress().getPostalCode()))
                        .collect(Collectors.toList());
                }
        }

        return quotesByClient == null
            ? new SearchQuoteResult()
            : createSearchQuoteResult(quotesByClient);
    }
}
