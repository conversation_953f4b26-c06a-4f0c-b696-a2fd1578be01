package com.intactfc.bloom.facade;

import com.intact.dialer.service.IDialerService;
import com.intactfc.bloom.converter.QuoteDTOToQuoteUIDTOConverter;
import com.intactfc.bloom.domain.FollowUpMessage;
import com.intactfc.bloom.domain.QuoteUIDTO;
import com.intactfc.bloom.domain.UserAccount;
import com.intactfc.bloom.domain.DialerResponseDTO;
import com.intactfc.bloom.domain.DialerDTO;
import com.intactfc.bloom.exceptions.BloomRepositoryServiceException;
import com.intactfc.bloom.exceptions.DialerServiceException;
import com.intactfc.bloom.exceptions.QuoteNotFoundException;
import com.intactfc.bloom.service.BloomRepositoryClient;
import com.intactfc.bloom.utils.FollowUpMessageHelper;
import intact.bloom.api.client.model.QuoteDTO;
import intact.bloom.api.client.model.QuoteDTO.WorkFlowStatusEnum;
import intact.bloom.dialer.service.client.api.ApiException;
import intact.bloom.dialer.service.client.model.QuoteDialerDTO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.owasp.esapi.ESAPI;
import org.owasp.esapi.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.convert.converter.Converter;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.LinkedList;
import java.util.List;
import java.util.stream.Collectors;

@Component
public class DialerFacade {

    private static Logger log = ESAPI.getLogger(DialerFacade.class);

    @Autowired
    private BloomRepositoryClient bloomRepositoryClient;

    @Autowired
    private Converter<QuoteDTO, QuoteDialerDTO> quoteDTOToQuoteDialerDTOConverter;

    @Autowired
    private IDialerService dialerService;

    @Autowired
    RestTemplate restTemplate;

    @Autowired
    private QuoteDTOToQuoteUIDTOConverter converter;

    @Value("${bloom-plp-processor.url.update}")
    private String bloomPlpUrl;

    @Value("${bloom-xpas-processor.url.update}")
    private String bloomXpasUrl;

    @Value("${bloom-contact-cl-processor.url.update}")
    private String bloomContactClUrl;
    
    @Autowired
    private UserAccount userAccount;

    public DialerResponseDTO sendToDialer(DialerDTO dialerDTO) {

        DialerResponseDTO dialerResponseDTO = new DialerResponseDTO();
        List<QuoteDTO> listLinkedQuote = new LinkedList<>();
        List<QuoteDTO> listDeletedQuote = new LinkedList<>();

        String [] linkedQuoteNumbers = dialerDTO.getLinkedQuoteNumbers();
        String [] deletedQuoteNumbers = dialerDTO.getDeletedQuoteNumbers();

        log.debug(Logger.EVENT_SUCCESS, "Searching quote [id:%s]".formatted(dialerDTO.getUuid()));

        QuoteDTO quoteSentToDialer = searchQuotesByUuid(dialerDTO.getUuid());

        log.debug(Logger.EVENT_SUCCESS, "Successfully found quote [id:%s]".formatted(dialerDTO.getUuid()));

        if(quoteSentToDialer == null){
            log.error(Logger.EVENT_SUCCESS, "The quote with the id %s was not found.".formatted(dialerDTO.getUuid()));
            throw new QuoteNotFoundException("The quote with the id " + dialerDTO.getUuid() + " was not found.");
        }

        //If we have a new quote in the meantime of our treatment, we go back to the quote treatment
        List<QuoteDTO> listAllExistingQuoteDTO = searchQuotesToBeReviewed(
                quoteSentToDialer.getPhone() != null
                        ? quoteSentToDialer.getPhone().getAreaCode() + quoteSentToDialer.getPhone().getPhoneNumber()
                        : null);
        
        if ((ArrayUtils.getLength(linkedQuoteNumbers) + ArrayUtils.getLength(deletedQuoteNumbers) + 1) != CollectionUtils.size(listAllExistingQuoteDTO)) {

            listAllExistingQuoteDTO = listAllExistingQuoteDTO.stream()
                    .filter(quote -> !StringUtils.equals(quote.getQuoteNumber(), quoteSentToDialer.getQuoteNumber()))
                    .filter(quote -> !ArrayUtils.contains(linkedQuoteNumbers, quote.getQuoteNumber()))
                    .filter(quote -> !ArrayUtils.contains(deletedQuoteNumbers, quote.getQuoteNumber()))
                    .collect(Collectors.toList());

            dialerResponseDTO.setNewLinkedQuotesUIDTO(convertToQuoteUIDTOs(listAllExistingQuoteDTO));

            return dialerResponseDTO;
        }

        // convert quoteParent to quoteDialerDTO
        QuoteDialerDTO quoteDialerToBeUpdated = quoteDTOToQuoteDialerDTOConverter.convert(quoteSentToDialer);

        if (quoteDialerToBeUpdated != null) {
            quoteDialerToBeUpdated.setUserName(userAccount.getUserId());

            // set List of child quotes
            if (linkedQuoteNumbers != null) {
                quoteDialerToBeUpdated.setListRelatedQuotes(Arrays.asList(linkedQuoteNumbers));
            }

            String dialerConfirmationNumber = sendToDialer(quoteDialerToBeUpdated);

            updateQuote(Arrays.asList(quoteSentToDialer),dialerConfirmationNumber,linkedQuoteNumbers,QuoteDTO.WorkFlowStatusEnum.SENT_DIALER);

            if(linkedQuoteNumbers != null){
                Arrays.stream(linkedQuoteNumbers).forEach(quoteNumber -> CollectionUtils.addIgnoreNull(listLinkedQuote, searchQuotesByQuoteNumber(quoteNumber)));

                // we add the "send to dialer" quote to linkedQuoteNumbers to update the linked quote notes
                ArrayUtils.add(linkedQuoteNumbers, quoteSentToDialer.getQuoteNumber());

                updateQuote(listLinkedQuote,dialerConfirmationNumber,linkedQuoteNumbers,QuoteDTO.WorkFlowStatusEnum.LINKED);
            }

            if(deletedQuoteNumbers != null) {

                Arrays.stream(deletedQuoteNumbers).forEach(quoteNumber -> CollectionUtils.addIgnoreNull(listDeletedQuote, searchQuotesByQuoteNumber(quoteNumber)));

                updateQuote(listDeletedQuote,dialerConfirmationNumber,linkedQuoteNumbers,QuoteDTO.WorkFlowStatusEnum.DELETED);
            }

            dialerResponseDTO.setDialerConfirmationNumber(dialerConfirmationNumber);
        }

        return dialerResponseDTO;
    }

    private String sendToDialer(QuoteDialerDTO quoteDialerDTO){
        String dialerConfirmationNumber;

        try {
            log.debug(Logger.EVENT_SUCCESS, "Sending [quoteNumber:%s] to dialer".formatted(quoteDialerDTO.getQuoteNumber()));

           dialerConfirmationNumber = dialerService.send(quoteDialerDTO);

           log.debug(Logger.EVENT_SUCCESS, "Successfully sent [quoteNumber:%s]".formatted(quoteDialerDTO.getQuoteNumber()));

        } catch (ApiException apiEx) {
            log.error(Logger.EVENT_FAILURE, "Error occured when calling dialer service with quoteNumber = %s.".formatted(quoteDialerDTO.getQuoteNumber()), apiEx);
            throw new DialerServiceException("Error occured when calling dialer service with quoteNumber =  " + quoteDialerDTO.getQuoteNumber(), apiEx);
        }

        if ("-1".equals(dialerConfirmationNumber)) {
            log.error(Logger.EVENT_FAILURE, "Dialer service returned -1 as confirmation number. This indicates an error within the dialer corpo service. The quote number is %s.".formatted(quoteDialerDTO.getQuoteNumber()));
            throw new DialerServiceException("Dialer service returned -1 as confirmation number. This indicates an error within the dialer corpo service.");
        }

        return dialerConfirmationNumber;
    }

    private void updateQuote(List<QuoteDTO> listQuotes, String dialerConfirmationNumber, String[] quoteNumbers, QuoteDTO.WorkFlowStatusEnum workFlowStatus){
        listQuotes.forEach(quoteToBeUpdated -> {
            quoteToBeUpdated.setConfirmationNumber(dialerConfirmationNumber);
            QuoteDialerDTO quoteDialerToBeUpdated = quoteDTOToQuoteDialerDTOConverter.convert(quoteToBeUpdated);
            quoteToBeUpdated.setWorkFlowStatus(workFlowStatus);
            quoteDialerToBeUpdated.setListRelatedQuotes(quoteNumbers != null ? Arrays.asList(quoteNumbers) : null);

            partialUpdateQuote(quoteToBeUpdated,dialerConfirmationNumber);
            
            quoteDialerToBeUpdated.setUserName(this.userAccount.getUserId());
            updateFollowUpMessage(quoteDialerToBeUpdated, workFlowStatus, dialerConfirmationNumber);

        });
    }

    private void partialUpdateQuote(QuoteDTO quoteToBeUpdated, String dialerConfirmationNumber) {
        try {
            bloomRepositoryClient.partialUpdateQuote(quoteToBeUpdated);
        } catch (intact.bloom.api.client.api.ApiException ex) {
            log.error(Logger.EVENT_FAILURE, "exception occured when trying to update quote with dialerConfirmationNumber = %s ".formatted(dialerConfirmationNumber), ex);
            throw new BloomRepositoryServiceException("exception occured when trying to update quote with dialerConfirmationNumber {} " + dialerConfirmationNumber, ex);
        }
    }

    private void updateFollowUpMessage(QuoteDialerDTO quoteDialerToBeUpdated, QuoteDTO.WorkFlowStatusEnum workFlowStatus, String dialerConfirmationNumber){
        try {
            //Update Follow Up Status for WebZone. Temporary Code. To be deleted
            FollowUpMessage followUpMessage = FollowUpMessageHelper.buildFollowUpMessage(quoteDialerToBeUpdated, workFlowStatus, dialerConfirmationNumber);
            restTemplate.postForObject(getUrl(quoteDialerToBeUpdated.getDatasourceOrigin()), followUpMessage, Boolean.class);

        } catch (Exception ex) {
            log.info(Logger.EVENT_FAILURE, "the quote with the number : %s was not updated".formatted(quoteDialerToBeUpdated.getQuoteNumber()),ex);
        }
    }

    private String getUrl(QuoteDialerDTO.DatasourceOriginEnum datasourceOrigin) {
        if (FollowUpMessageHelper.isAuto(datasourceOrigin))
            return bloomPlpUrl;
        else if (FollowUpMessageHelper.isHome(datasourceOrigin))
            return bloomXpasUrl;
        else
            return bloomContactClUrl;
    }

    private List<QuoteDTO> searchQuotesToBeReviewed(String phoneNumber) {
        List<QuoteDTO> quoteDTOs;

        try {
            quoteDTOs = bloomRepositoryClient.searchQuotesByPhone(phoneNumber);
        } catch (Exception ex) {
            log.error(Logger.EVENT_FAILURE, "Exception when calling Search quotes with phoneNumber = %s".formatted(phoneNumber), ex);
            throw new BloomRepositoryServiceException("Exception when calling Search quotes with phoneNumber = " + phoneNumber, ex);        
        }

        return CollectionUtils.isEmpty(quoteDTOs)
                ? new ArrayList<>()
                : quoteDTOs.stream()
                    .filter(quote -> quote.getWorkFlowStatus() != null && quote.getWorkFlowStatus() == WorkFlowStatusEnum.READY)
                    .filter(quote ->  quote.getCreationTimeStamp().isAfter(DateTime.now().minusMonths(2)))
                    .collect(Collectors.toList());
    }

    private QuoteDTO searchQuotesByUuid(String uuid) {
        QuoteDTO quoteDTO;

        try {
            quoteDTO = bloomRepositoryClient.searchQuoteByUuid(uuid);
        } catch (Exception ex) {
            log.error(Logger.EVENT_FAILURE, "Exception when calling Search quotes with uuid = %s.".formatted(uuid), ex);
            throw new BloomRepositoryServiceException("Exception when calling Search quotes with uuid = " + uuid, ex);
        }

        return quoteDTO;
    }

    private QuoteDTO searchQuotesByQuoteNumber(String quoteNumber) {
        QuoteDTO quoteDTO;

        try {
            quoteDTO = bloomRepositoryClient.searchQuotesByQuoteNumber(quoteNumber);
        } catch (Exception ex) {
            log.error(Logger.EVENT_FAILURE, "Exception when calling Search quotes with quoteNumber = %s.".formatted(quoteNumber), ex);
            throw new BloomRepositoryServiceException("Exception when calling Search quotes with quoteNumber = " + quoteNumber, ex);        }

        return quoteDTO;
    }

    private List<QuoteUIDTO> convertToQuoteUIDTOs(List<QuoteDTO> quoteDTOs) {
        List<QuoteUIDTO> quoteUIDTOs = null;

        if (CollectionUtils.isNotEmpty(quoteDTOs)) {
            quoteUIDTOs = quoteDTOs.stream().map(result -> converter.convert(result)).collect(Collectors.toList());

            log.info(Logger.EVENT_FAILURE, "Search successful %s results found".formatted(quoteUIDTOs.size()));
        }

        return quoteUIDTOs;
    }
}
