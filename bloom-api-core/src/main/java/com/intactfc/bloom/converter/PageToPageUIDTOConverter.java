package com.intactfc.bloom.converter;

import com.intactfc.bloom.domain.PageUIDTO;
import intact.bloom.api.client.model.Page;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.convert.converter.Converter;
import org.springframework.stereotype.Component;

import java.util.stream.Collectors;

@Component
public class PageToPageUIDTOConverter implements Converter<Page, PageUIDTO> {

    @Autowired
    private QuoteDTOToQuoteUIDTOConverter converter;

    @Override
    public PageUIDTO  convert(Page page) {
        PageUIDTO pageUIDTO = new PageUIDTO();

        if(CollectionUtils.isNotEmpty(page.getContent()))
            pageUIDTO.setContent(page.getContent().stream().map(result -> converter.convert(result)).collect(Collectors.toList()));

        pageUIDTO.setNumber(page.getNumber());
        pageUIDTO.setSize(page.getSize());
        pageUIDTO.setTotalElements(page.getTotalElements());

        return pageUIDTO;
    }
}
