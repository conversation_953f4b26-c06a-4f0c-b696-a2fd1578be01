package intact.test.bloom.integration.models;

import intact.test.integration.models.dto.AbstractResponseDTO;
import intact.test.integration.models.dto.ErrorDTO;
import java.util.ArrayList;
import java.util.List;
import lombok.Data;

@Data
public class ClientsResponseDTO extends AbstractResponseDTO<List<QuoteLeadDTO>, ErrorDTO> {
  private List<ClientDTO> clients = new ArrayList<>();

  @Override
  public boolean hasNullData() {
    return this.getData() == null;
  }

}
