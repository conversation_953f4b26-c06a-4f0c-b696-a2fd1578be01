package com.intactfc.bloom.converter;

import com.intactfc.bloom.domain.QuoteUIDTO;
import com.intactfc.bloom.mock.quote.MockQuoteDTO;
import intact.bloom.api.client.model.*;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.text.WordUtils;
import org.joda.time.DateTime;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.test.util.ReflectionTestUtils;

import static org.junit.jupiter.api.Assertions.*;

class QuoteDTOToQuoteUIDTOConverterTest {

    private QuoteDTOToQuoteUIDTOConverter converter;

    private QuoteDTO entryQuote;

    private String testFirstName = "FirstName";

    private String testLastName = "LastName";

    @BeforeEach
    void setup() {
        entryQuote = MockQuoteDTO.createQuoteDTO();
        converter = new QuoteDTOToQuoteUIDTOConverter();
        ReflectionTestUtils.setField(converter,"phoneConverter",new PhoneDTOToStringConverter());
    }

    @Test
    void convertingAQuote_withANullObject_shouldReturnNull() {
        QuoteUIDTO resultQuote = converter.convert(null);

        assertNull(resultQuote,"Resulting quote should be null");
    }

    @Test
    void convertingAQuote_withAnEmptyQuoteDTO_shouldReturnAnEmptyQuoteUIDTO() {
        entryQuote = new QuoteDTO();

        QuoteUIDTO resultQuote = converter.convert(entryQuote);

        this.assertQuoteConversionWasSuccessful(entryQuote,resultQuote);

    }

    @Test
    void convertingAQuote_withQuoteDTOWithFilledAttributes_shouldReturnCorrespondingQuoteUIDTO() {
        entryQuote = this.createBasicEntryQuote();
        entryQuote.setParty(this.createBasicEntryParty());

        QuoteUIDTO resultQuote = converter.convert(entryQuote);

        this.assertQuoteConversionWasSuccessful(entryQuote,resultQuote);
    }

    @Test
    void convertingAQuote_withAnEmptyParty_shouldReturnAnEmptyName() {
        entryQuote = this.createBasicEntryQuote();
        entryQuote.setParty(new PartyDTO());

        QuoteUIDTO resultQuote = converter.convert(entryQuote);

        this.assertQuoteConversionWasSuccessful(entryQuote,resultQuote);
    }

    @Test
    void convertingAQuote_withAPartyWithANullFirstName_shouldOnlySetLastNameInFullName() {
        entryQuote = this.createBasicEntryQuote();
        entryQuote.setParty(this.createBasicEntryParty());
        entryQuote.getParty().setFirstName(null);

        QuoteUIDTO resultQuote = converter.convert(entryQuote);

        this.assertQuoteConversionWasSuccessful(entryQuote,resultQuote);
    }

    @Test
    void convertingAQuote_withAPartyWithANullLastName_shouldOnlySetFirstNameInFullName() {
        entryQuote = this.createBasicEntryQuote();
        entryQuote.setParty(this.createBasicEntryParty());
        entryQuote.getParty().setLastName(null);

        QuoteUIDTO resultQuote = converter.convert(entryQuote);

        this.assertQuoteConversionWasSuccessful(entryQuote,resultQuote);
    }

    @Test
    void convertingAQuote_withAnEmptyPhone_shouldReturnAnEmptyPhoneNumber() {
        entryQuote = this.createBasicEntryQuote();
        entryQuote.setPhone(new PhoneDTO());

        QuoteUIDTO resultQuote = converter.convert(entryQuote);

        this.assertQuoteConversionWasSuccessful(entryQuote,resultQuote);
    }

    @Test
    void convertQuoteDTOToQuoteUIDTO_withCompleteQuoteStatus_shouldReturnQuoteComplete() {

        entryQuote = MockQuoteDTO.createQuoteDTO();

        QuoteUIDTO resultQuote = converter.convert(entryQuote);

        assertNotNull(resultQuote);
        assertFalse(resultQuote.getIsQuoteIncomplete());
        assertTrue(resultQuote.getIsQuoteComplete());
    }

    @Test
    void convertQuoteDTOToQuoteUIDTO_withInProgressReadyToBindQuoteStatus_shouldReturnQuoteComplete() {

        entryQuote = MockQuoteDTO.createQuoteDTO();
        entryQuote.setQuoteStatus(QuoteDTO.QuoteStatusEnum.IN_PROGRESS_READY_TO_BIND);

        QuoteUIDTO resultQuote = converter.convert(entryQuote);

        assertNotNull(resultQuote);
        assertFalse(resultQuote.getIsQuoteIncomplete());
        assertTrue(resultQuote.getIsQuoteComplete());
    }

    @Test
    void convertQuoteDTOToQuoteUIDTO_withInForceQuoteStatus_shouldReturnQuoteComplete() {

        entryQuote = MockQuoteDTO.createQuoteDTO();
        entryQuote.setQuoteStatus(QuoteDTO.QuoteStatusEnum.IN_FORCE);

        QuoteUIDTO resultQuote = converter.convert(entryQuote);

        assertNotNull(resultQuote);
        assertFalse(resultQuote.getIsQuoteIncomplete());
        assertTrue(resultQuote.getIsQuoteComplete());
    }

    @Test
    void convertQuoteDTOToQuoteUIDTO_withIncompleteQuoteStatus_shouldReturnQuoteIncomplete() {

        entryQuote = MockQuoteDTO.createQuoteDTO();
        entryQuote.setQuoteStatus(QuoteDTO.QuoteStatusEnum.QUOTE_INCOMPLETE);

        QuoteUIDTO resultQuote = converter.convert(entryQuote);

        assertNotNull(resultQuote);
        assertTrue(resultQuote.getIsQuoteIncomplete());
        assertFalse(resultQuote.getIsQuoteRoadBlock());
    }

    @Test
    void convertQuoteDTOToQuoteUIDTO_withAreadyClientQuoteStatus_return_isQuoteAlReadyClient_true() {

        entryQuote = MockQuoteDTO.createQuoteDTO();
        entryQuote.setQuoteStatus(QuoteDTO.QuoteStatusEnum.ALREADY_CLIENT);

        QuoteUIDTO resultQuote = converter.convert(entryQuote);

        assertNotNull(resultQuote);
        assertTrue(resultQuote.getIsQuoteAlReadyClient());
        assertFalse(resultQuote.getIsQuoteIncomplete());
        assertFalse(resultQuote.getIsQuoteRoadBlock());
        assertFalse(resultQuote.getIsQuoteToBeReviewed());

    }

    @Test
    void convertQuoteDTOToQuoteUIDTO_with_roadblock_quoteStatus_return_isQuoteRoadBlock_true() {

        entryQuote = MockQuoteDTO.createQuoteDTO();
        entryQuote.setQuoteStatus(QuoteDTO.QuoteStatusEnum.ROAD_BLOCK);

        QuoteUIDTO resultQuote = converter.convert(entryQuote);

        assertNotNull(resultQuote);
        assertTrue(resultQuote.getIsQuoteRoadBlock());
        assertFalse(resultQuote.getIsQuoteIncomplete());
        assertFalse(resultQuote.getIsQuoteAlReadyClient());
        assertFalse(resultQuote.getIsQuoteToBeReviewed());
    }

    @Test
    void convertQuoteDTOToQuoteUIDTO_with_to_relatedInd_true_return_isToBeReviewed_true() {

        entryQuote = MockQuoteDTO.createQuoteDTO();
        entryQuote.mostRecentInd(true);
        entryQuote.relatedInd(true);

        QuoteUIDTO resultQuote = converter.convert(entryQuote);

        assertNotNull(resultQuote);
        assertFalse(resultQuote.getIsQuoteRoadBlock());
        assertFalse(resultQuote.getIsQuoteIncomplete());
        assertFalse(resultQuote.getIsQuoteAlReadyClient());
        assertTrue(resultQuote.getIsQuoteToBeReviewed());
    }
    /**
     * Method used to create a basic QuoteDTO as entry for tests.
     * @return Basic QuoteDTO object with all attributes filled
     */
    private QuoteDTO createBasicEntryQuote(){
        QuoteDTO basicQuote = new QuoteDTO();

        basicQuote.setId("TestID987654123");

        //TODO : DateTIme.now() is not well formatted. have to check why
        basicQuote.setCreationTimeStamp(DateTime.now());
        basicQuote.setWorkFlowStatus(QuoteDTO.WorkFlowStatusEnum.READY);

        return basicQuote;
    }

    /**
     * Method used to create a basic PartyDTO as entry for tests.
     * @return Basic PartyDTO object with all attributes filled
     */
    private PartyDTO createBasicEntryParty(){
        PartyDTO basicParty = new PartyDTO();

        basicParty.setFirstName(this.testFirstName);
        basicParty.setLastName(this.testLastName);
        basicParty.setAddress(createBasicEntryAdress());
        return basicParty;
    }

    /**
     * Method used to create a basic AddressDTO as entry for tests.
     * @return Basic AdressDTO object with all attributes filled
     */
    private AddressDTO createBasicEntryAdress(){
        AddressDTO basicAddress = new AddressDTO();
        basicAddress.setProvince("QC");
        basicAddress.setPostalCode("H1H1H1");
        basicAddress.setCity("Montreal");
        basicAddress.setApptNumber("1234");
        basicAddress.setStreetName("test street");
        return basicAddress;
    }

    /**
     * Method used to validate that each field have been transferred correctly from the entry QuoteDTO object
     * to the result QuoteUIDTO object
     * @param entryQuote QuoteDTO before conversion
     * @param resultQuote QuoteUIDTO after conversion
     */
    private void assertQuoteConversionWasSuccessful(QuoteDTO entryQuote, QuoteUIDTO resultQuote) {

        String fullName = entryQuote.getParty() != null ? WordUtils.capitalizeFully("%s %s".formatted(
                entryQuote.getParty().getFirstName() != null ? entryQuote.getParty().getFirstName() : StringUtils.EMPTY,
                entryQuote.getParty().getLastName() != null ? entryQuote.getParty().getLastName() : StringUtils.EMPTY),
                '-',' ') : StringUtils.EMPTY;

        String phoneNumber = entryQuote.getPhone() == null
                ? StringUtils.EMPTY
                : (new StringBuilder())
                .append(entryQuote.getPhone().getAreaCode() != null
                        ? entryQuote.getPhone().getAreaCode()
                        : StringUtils.EMPTY)
                .append(entryQuote.getPhone().getAreaCode() != null && entryQuote.getPhone().getPhoneNumber() != null
                        ? " "
                        : StringUtils.EMPTY)
                .append(entryQuote.getPhone().getPhoneNumber() != null && entryQuote.getPhone().getPhoneNumber().length() >= 3
                        ?  new StringBuffer(entryQuote.getPhone().getPhoneNumber()).insert(3, "-").toString()
                        : StringUtils.EMPTY)
                .toString();

        String province = entryQuote.getParty() != null && entryQuote.getParty().getAddress() != null ? entryQuote.getParty().getAddress().getProvince() : null;
        String apptNumber = entryQuote.getParty() != null && entryQuote.getParty().getAddress() != null ? entryQuote.getParty().getAddress().getApptNumber() : null;
        String city = entryQuote.getParty() != null && entryQuote.getParty().getAddress() != null ? entryQuote.getParty().getAddress().getCity() : null;
        String streetName = entryQuote.getParty() != null && entryQuote.getParty().getAddress() != null ? entryQuote.getParty().getAddress().getStreetName() : null;

        assertNotNull(resultQuote, "Resulting quote should not be null");
        assertEquals(entryQuote.getId(), resultQuote.getId());
        assertEquals(fullName, resultQuote.getName());
        assertEquals(province, resultQuote.getAddress().getProvince());
        assertEquals(apptNumber, resultQuote.getAddress().getApptNumber());
        assertEquals(city, resultQuote.getAddress().getCity());
        assertEquals(streetName, resultQuote.getAddress().getStreetName());

        //assertEquals(entryQuote.getCreationTimeStamp(), resultQuote.getDate());
        assertEquals(phoneNumber, resultQuote.getPhoneNumber());
    }

    @Test
    void test_convert_shoul_return_brokerName_and_quote_number() {
        QuoteUIDTO quoteUIDTO = converter.convert(entryQuote);

        assertEquals("BOW VALLEY INSURANCE SERVICES",quoteUIDTO.getBroker().getName());
        assertEquals("QF123456798",quoteUIDTO.getQuoteNumber());
    }

    @Test
    void test_convert_with_null_vehicleDTO_in_quoteDTO_should_be_empty_list_in_quoteUIDTO() {
        entryQuote.setListVehiclesDTO(null);
        QuoteUIDTO quoteUIDTO = converter.convert(entryQuote);

        this.assertQuoteConversionWasSuccessful(entryQuote,quoteUIDTO);

        assertEquals(0,quoteUIDTO.getVehicles().size());
    }

    @Test
    void test_convert_with_two_vehicleDTO_in_quoteDTO_should_be_the_firt_one_in_quoteUIDTO() {
        VehicleDTO vehicleDTO = new VehicleDTO();
        vehicleDTO.setCode("85201");
        vehicleDTO.setMake("Nissan");
        vehicleDTO.setYear("2001");
        vehicleDTO.setModel("haskai");
        entryQuote = MockQuoteDTO.createQuoteDTO();
        entryQuote.addListVehiclesDTOItem(vehicleDTO);

        QuoteUIDTO quoteUIDTO = converter.convert(entryQuote);

        assertEquals("123456",quoteUIDTO.getVehicles().getFirst().getCode());
        assertEquals("toyota",quoteUIDTO.getVehicles().getFirst().getMake());
        assertEquals("sienna",quoteUIDTO.getVehicles().getFirst().getModel());
        assertEquals("2019",quoteUIDTO.getVehicles().getFirst().getYear());
    }

    @Test
    void test_convert_with_sent_quote_to_dialer_should_have_isHidden_true() {

        QuoteDTO quoteDTO = MockQuoteDTO.createQuoteDTO();
        quoteDTO.setWorkFlowStatus(QuoteDTO.WorkFlowStatusEnum.SENT_DIALER);

        QuoteUIDTO quoteUIDTO = converter.convert(quoteDTO);

        assertTrue(quoteUIDTO.getIsHidden());
    }

    @Test
    void test_convert_with_uploaded_quote_should_have_isHidden_true() {

        QuoteDTO quoteDTO = MockQuoteDTO.createQuoteDTO();
        quoteDTO.setWorkFlowStatus(QuoteDTO.WorkFlowStatusEnum.UPLOADED);

        QuoteUIDTO quoteUIDTO = converter.convert(quoteDTO);

        assertTrue(quoteUIDTO.getIsHidden());
    }

    @Test
    void test_convert_with_DELETES_quote_should_have_isHidden_true() {

        QuoteDTO quoteDTO = MockQuoteDTO.createQuoteDTO();
        quoteDTO.setWorkFlowStatus(QuoteDTO.WorkFlowStatusEnum.DELETED);

        QuoteUIDTO quoteUIDTO = converter.convert(quoteDTO);

        assertTrue(quoteUIDTO.getIsHidden());
    }

    @Test
    void test_convert_with_NO_FWP_NEEDED_quote_should_have_isHidden_true() {

        QuoteDTO quoteDTO = MockQuoteDTO.createQuoteDTO();
        quoteDTO.setWorkFlowStatus(QuoteDTO.WorkFlowStatusEnum.DELETED);

        QuoteUIDTO quoteUIDTO = converter.convert(quoteDTO);

        assertTrue(quoteUIDTO.getIsHidden());
    }

    @Test
    void test_convert_with_READY_quote_should_have_isHidden_false() {

        QuoteDTO quoteDTO = MockQuoteDTO.createQuoteDTO();
        quoteDTO.setWorkFlowStatus(QuoteDTO.WorkFlowStatusEnum.READY);

        QuoteUIDTO quoteUIDTO = converter.convert(quoteDTO);

        assertFalse(quoteUIDTO.getIsHidden());
    }

    @Test
    void test_convert_with_Linked_quote_should_have_isHidden_false() {

        QuoteDTO quoteDTO = MockQuoteDTO.createQuoteDTO();
        quoteDTO.setWorkFlowStatus(QuoteDTO.WorkFlowStatusEnum.LINKED);

        QuoteUIDTO quoteUIDTO = converter.convert(quoteDTO);

        assertTrue(quoteUIDTO.getIsHidden());
    }
}
