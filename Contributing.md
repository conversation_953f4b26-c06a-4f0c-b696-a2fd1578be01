## How developers should contribute to the project:

### Branching model

Project repository is `https://githubifc.iad.ca.inet/lab-se/bloom-api`
The main project branch is `main`. Create every new branch from `main`.
The branch name convention is "feat/ | chore/ | fix/ + jira number + short description". ex: `feat/mars-2506-do-something`.

### Testing

Refer to [this](https://confluence.tooling.intactfc.cloud/display/CHECK/Test+Naming+Convention) page for test naming
convention

### Code Style

Refer to [this](https://confluence.tooling.intactfc.cloud/display/CHECK/Backend+Coding+Standards) page for more details

### Commit Messages instructions

Refer to [this](https://confluence.tooling.intactfc.cloud/display/CHECK/Git+Naming+Convention) page for more details

### Pull Requests ( Submit & Reviews)

Refer to [this](https://confluence.tooling.intactfc.cloud/display/CHECK/Code+Review) page for more details
