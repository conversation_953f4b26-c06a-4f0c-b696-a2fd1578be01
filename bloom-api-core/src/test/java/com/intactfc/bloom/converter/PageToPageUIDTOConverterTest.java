package com.intactfc.bloom.converter;

import com.intactfc.bloom.domain.PageUIDTO;
import intact.bloom.api.client.model.ListQuoteDTO;
import intact.bloom.api.client.model.Page;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.assertEquals;

@ExtendWith(MockitoExtension.class)
class PageToPageUIDTOConverterTest {

    private PageToPageUIDTOConverter pageToPageUIDTOConverter = new PageToPageUIDTOConverter();

    @Test
    void convert_wiht_area_code_and_phone_number() {

        Page page = new Page();
        page.setContent(new ListQuoteDTO());
        page.setNumber(2);
        page.setSize(3);
        page.setTotalElements(6);
        PageUIDTO result = pageToPageUIDTOConverter.convert(page);

        assertEquals("2",result.getNumber().toString());
        assertEquals("3",result.getSize().toString());
        assertEquals("6",result.getTotalElements().toString());

    }
}
