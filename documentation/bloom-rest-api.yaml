---
swagger: "2.0"
info:
  title: "Bloom REST API Service Contract"
  description: "This is the API Swagger Contract for the Bloom REST API Service"
  version: "1.0.0-SNAPSHOT"
  termsOfService: "http://www.github.com/kongchen/swagger-maven-plugin"
  contact:
    name: "Spider Web team"
    url: "https://intactlab.atlassian.net/wiki/spaces/SW/overview"
    email: "<EMAIL>"
  license:
    name: "Apache 2.0"
    url: "http://www.apache.org/licenses/LICENSE-2.0.html"
host: "bloom.intact.net"
schemes:
  - "https"
basePath: "/bloom/v1"
produces:
  - application/json
paths:
  '/quotes':
    get:
      summary: 'Search quote'
      description: |-
        The Search quote endpoint returns a list of quote objects that satisfies the search criteria.
      tags:
        - "bloom-rest-api-service"
      parameters:
        - name: "creationMinAgoFrom"
          in: "query"
          required: true
          type: "string"
          description: quotes from minutes ago
        - name: "creationMinAgoTo"
          in: "query"
          required: true
          type: "string"
          description: quotes until minutes ago
      responses:
        200:
          description: "At least one quote satisfying the search criteria was found."
          schema:
            $ref: "#/definitions/ListQuoteUIDTO"
        204:
          description: "No quote was found for the search criteria."
        400:
          description: "Validation errors were found."
          schema:
            $ref: "#/definitions/ListErrorUIDTO"
        500:
          description: "An exception occured."
          schema:
            $ref: "#/definitions/ListErrorUIDTO"
    put:
      summary: 'Update quote'
      description: |-
        The Update quote endpoint is used to update a QuoteDTO with updatable infos from the corresponding QuoteUIDTO object
      tags:
        - "bloom-repository-service"
      operationId: "update"
      parameters:
        -   in: "body"
            name: "body"
            required: true
            schema:
              $ref: "#/definitions/QuoteUIDTO"
      responses:
        200:
          description: "A successful update of the Quote was done in the database."
        400:
          description: "Jackson Validation errors were found."
          schema:
            $ref: "#/definitions/ListErrorUIDTO"
        500:
          description: "An exception occured."
          schema:
            $ref: "#/definitions/ListErrorUIDTO"
securityDefinitions:
  basicAuth:
    type: "basic"
definitions:
  ErrorUIDTO:
    type: "object"
    properties:
      code:
        type: "string"
      message:
        type: "string"
  QuoteUIDTO:
    type: "object"
    properties:
      id:
        type: "string"
        description: Quote Id
      clientName:
        type: "string"
        description: policy holder first name and last name
      phoneNumber:
        type: "string"
        description: format is xxx-xxx-xxxx
      creationTimeStamp:
        type: "string"
        description: format is yyyy-MM-ddHH:mm:ss
      isHidden:
        type: "boolean"
        description: Flag to hide quote in UI
  ListQuoteUIDTO:
    type: "array"
    items:
      $ref: "#/definitions/QuoteUIDTO"
  ListErrorUIDTO:
    type: "array"
    items:
      $ref: "#/definitions/ErrorUIDTO"
