# Bloom API
[![CI](https://githubifc.iad.ca.inet/lab-se/bloom-api/actions/workflows/ci.yaml/badge.svg)](https://githubifc.iad.ca.inet/lab-se/bloom-api/actions/workflows/ci.yaml)
[![Quality Gate Status](https://sonarqube-enterprise.dsso.intactfc.com/sonarqube/api/project_badges/measure?project=bloom-api&metric=alert_status&token=sqb_560c83ef102cf9b85e960d3d5e12032fbdc9a17b)](https://sonarqube-enterprise.dsso.intactfc.com/sonarqube/dashboard?id=bloom-api)
[![Coverage](https://sonarqube-enterprise.dsso.intactfc.com/sonarqube/api/project_badges/measure?project=bloom-api&metric=coverage&token=sqb_560c83ef102cf9b85e960d3d5e12032fbdc9a17b)](https://sonarqube-enterprise.dsso.intactfc.com/sonarqube/dashboard?id=bloom-api)
[![Security Rating](https://sonarqube-enterprise.dsso.intactfc.com/sonarqube/api/project_badges/measure?project=bloom-api&metric=security_rating&token=sqb_560c83ef102cf9b85e960d3d5e12032fbdc9a17b)](https://sonarqube-enterprise.dsso.intactfc.com/sonarqube/dashboard?id=bloom-api)

| Development(DEV)                                                                                                                                                                                                | Staging(UAT)                                                                                                                                                                                          | Production(PROD)                                                                                                                                                                                              |
|-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| [![](https://argocd.intactlab.intactfc.cloud/api/badge?name=bloom-api-development-intg&revision=true&showAppName=true)](https://argocd.intactlab.intactfc.cloud/applications/argocd/bloom-api-development-intg) | [![](https://argocd.intactlab.intactfc.cloud/api/badge?name=bloom-api-staging-uat&revision=true&showAppName=true)](https://argocd.intactlab.intactfc.cloud/applications/argocd/bloom-api-staging-uat) | [![](https://argocd.intactlab.intactfc.cloud/api/badge?name=bloom-api-production-prod&revision=true&showAppName=true)](https://argocd.intactlab.intactfc.cloud/applications/argocd/bloom-api-production-prod) |

## What is it ?

Bloom Api provides endpoints to manage the quotes shown to the Intact Agent with bloom-ui and stored in the MongoDB queried with
bloom-repository-service and can send them to the broker-dialer-service.

## Who use it ?

- [Bloom UI](https://githubifc.iad.ca.inet/lab-se/bloom-ui)

## Project Goal

The goal of this project is to handle the events between the different services.

## Util links

- [Installation](./Installation.md)
- [Config](./Config.md)
- [Dependencies](./Dependencies.md)
- [Contributing](./Contributing.md)
- [Documentation](https://confluence.tooling.intactfc.cloud/display/BLO/%5BBLO%5D+System+Architecture)

## Built With
* To be built with [GitHub Actions](https://githubifc.iad.ca.inet/lab-se/bloom-api/actions)
