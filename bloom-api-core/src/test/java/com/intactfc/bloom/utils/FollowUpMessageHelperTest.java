package com.intactfc.bloom.utils;

import com.intactfc.bloom.enums.AgreementFollowUpStatusEnum;
import intact.bloom.api.client.model.QuoteDTO;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertEquals;

class FollowUpMessageHelperTest {

    @Test
    void test_getAgreementFollowUpStatusEnum_wiht_LINKED_WorkFlowStatus_should_return_CONT_BUT_NO_FLW() {

        AgreementFollowUpStatusEnum agreementFollowUpStatusEnum = FollowUpMessageHelper.getAgreementFollowUpStatusEnum(QuoteDTO.WorkFlowStatusEnum.LINKED);

        assertEquals(AgreementFollowUpStatusEnum.CONT_BUT_NO_FLW,agreementFollowUpStatusEnum);
    }
}
