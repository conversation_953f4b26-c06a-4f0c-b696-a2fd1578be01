package com.intactfc.bloom.domain;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.ArrayList;
import java.util.List;


@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ErrorUIDTO {

    private List<Details> errors = new ArrayList<>();

    private MetaData metaData;

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Details{
        private String code;
        private String message;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class MetaData{
        private String timeStamp;
    }
}
