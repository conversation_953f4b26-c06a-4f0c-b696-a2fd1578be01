## Installation

### Pre-Requisite

The application requires JDK 17 and maven wrapper.

IntelliJ's users must install the `Lombok Plugin`.

### Bloom Api installation

* To generate the jar application use the following command.

`mvn clean install`

![mvn_config.png](mvn_config.png)

* To run the application, you can start it from your IDE or use on of the following methods

1- Go at the root of the module `bloom-api`

and type the following command in a terminal : `mvn spring-boot:run -Dspring.profiles.active=local -Dserver.port=8090`

or

2-  Go to `bloom-api-core/target`

and type the following command in a terminal : `java -jar bloom-api-core.jar --spring.profiles.active=local -Dserver.port=8090`

or

3- Go to `Run > Edit Configuration > Spring Boot > BloomApiApplication (default name)`

* Add the following VM options :`-Dserver.port=8090`
* Add the following Active profiles :`local`

![run_config.png](run_config.png)

Run the spring-boot application
