@api
Feature: Bloom API Integration

  @ZKey=ACQ-T8221
  Scenario: Retrieve list of older quotes (/quotes/leadList)
    When the user retrieves list of leads 30 min to 2 months
    Then Bloom HTTP response code should be 200
    And list of leads contains records

  @ZKey=ACQ-T8222
  Scenario: Attempt to retrieve list of older quotes (/quotes/leadList), missing query param, should result in 500
    When the user attempts to retrieve list of leads missing required query param
    Then Bloom HTTP response code should be 500

  @ZKey=ACQ-T9262 @skip
  Scenario: Send quote to dialer ("/quotes/dialer")
    Given the user prepares a INT_AB_Car quote
    * the user gets their offer
    When the user sends to dialer quote number
    Then Bloom HTTP response code should be 204

  @ZKey=ACQ-T9263 @skip
  Scenario: Retrieve quote by phone number ("/quotes/search-by-phonenumber") DOES NOT WORK - MEG-4782
    Given the user prepares a INT_AB_Car quote
    * the user gets their offer
    When the user retrieves quotes by phone number
    Then Bloom HTTP response code should be 200
    * number of quotes returned 1 or more

  @ZKey=ACQ-T9264 @skip
  Scenario: Retrieve newly created quote by quote number (cba/quotes?quoteNumber) DOES NOT WORK - MEG-5162
    Given the user prepares a INT_AB_Car quote
    * the user gets their offer
    When the user retrieves new cba quote by quote number
    Then Bloom HTTP response code should be 200
    * number of cba quotes returned 1

  @ZKey=ACQ-T9265 @skip
  Scenario: Retrieve newly created quote by phone number (cba/quotes?phoneNumber) DOES NOT WORK - MEG-5162
    Given the user prepares a INT_AB_Car quote
    * the user gets their offer
    When the user retrieves new cba quote by phone number
    Then Bloom HTTP response code should be 200
    * number of cba quotes returned 1 or more

  @ZKey=ACQ-T9266 @skip
  Scenario: Retrieve newly created quote by client (cba/quotes/client) DOES NOT WORK - MEG-5162
    Given the user prepares a INT_AB_Car quote
    * the user gets their offer
    When the user retrieves new cba quote by client info
    Then Bloom HTTP response code should be 200
    * number of cba quotes returned 1 or more

  @ZKey=ACQ-T9267 @skip
  Scenario: Retrieve clients by name (cba/clients?name) DOES NOT WORK - MEG-5162
    Given the user prepares a INT_AB_Car quote
    * the user's full name contains random string
    * the user gets their offer
    When the user retrieves cba clients by name
    Then Bloom HTTP response code should be 200
    * number of cba clients returned 1 or more








