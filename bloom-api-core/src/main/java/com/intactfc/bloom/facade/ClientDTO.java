package com.intactfc.bloom.facade;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ClientDTO {
    private String name;
    private String firstName;
    private String lastName;
    private String companyName;
    private String phone;
    private String province;
    private String postalCode;
}
