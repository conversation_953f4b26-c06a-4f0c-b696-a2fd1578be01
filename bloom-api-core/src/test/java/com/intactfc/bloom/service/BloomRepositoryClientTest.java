package com.intactfc.bloom.service;

import com.intactfc.bloom.exceptions.BloomRepositoryServiceException;
import intact.bloom.api.client.api.ApiException;
import intact.bloom.api.client.api.BloomRepositoryServiceApi;
import intact.bloom.api.client.model.QuoteDTO;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;

import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class BloomRepositoryClientTest {

    @InjectMocks
    private BloomRepositoryClient bloomRepositoryClient;

    @Mock
    private BloomRepositoryServiceApi bloomRepositoryServiceApi;

    @Test
    void searchingQuotes_shouldCallApi() throws ApiException {
        String testCreationDateFrom = "2999-01-0100:00:00";
        String testCreationDateTo = "2999-02-0100:00:00";

        this.bloomRepositoryClient.searchQuotes(testCreationDateFrom,testCreationDateTo,null,null,new String[1]);

        verify(this.bloomRepositoryServiceApi,times(1)).searchQuotes(testCreationDateFrom,testCreationDateTo,null,null,Arrays.asList(new String[1]));
    }

    @Test
    void updatingQuote_shouldCallApi() throws ApiException {
        QuoteDTO updatedQuote = new QuoteDTO();

        this.bloomRepositoryClient.updateQuote(updatedQuote);

        verify(this.bloomRepositoryServiceApi,times(1)).upsert(updatedQuote);
    }

    @Test
    void PartiallyUpdatingQuote_shouldCallApi() throws ApiException {
        QuoteDTO updatedQuote = new QuoteDTO();

        this.bloomRepositoryClient.partialUpdateQuote(updatedQuote);

        verify(this.bloomRepositoryServiceApi,times(1)).partialUpdateQuote(updatedQuote);
    }

    @Test
    void retrievingQuoteById_shouldCallApi() throws ApiException {
        String testID = "quoteID_123456789";

        this.bloomRepositoryClient.searchQuotes(null,null,testID,null,new String[1]);

        verify(this.bloomRepositoryServiceApi,times(1)).searchQuotes(null,null,testID,null,Arrays.asList(new String[1]));
    }

    @Test
    void searchQuotesShouldReturnBloomRepoServiceException() throws ApiException {
        assertThrows(BloomRepositoryServiceException.class,() -> {
            String testID = "quoteID_123456789";
            when(bloomRepositoryClient.searchQuotes(null,null,testID,null,new String[1])).thenThrow(new BloomRepositoryServiceException(""));

            this.bloomRepositoryClient.searchQuotes(null,null,testID,null,new String[1]);
        });
    }
}
