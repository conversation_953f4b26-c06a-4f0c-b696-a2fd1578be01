package intact.test.bloom.integration.api;

import intact.test.bloom.integration.context.TestContext;
import intact.test.bloom.integration.models.QuotesRetrieveByClientInfoRequestDTO;
import intact.test.integration.models.api.AbstractAPI;
import intact.test.integration.context.RequestWrapper;
import io.restassured.http.Method;


public class BloomAP<PERSON> extends AbstractAPI<TestContext> {

  private final String BLOOM_URL = context.getEnvironmentUtils().getEnvProperty("bloom.url");

  public BloomAPI(TestContext context) {
    super(context);
  }


  public RequestWrapper quotesDialerRequest(String quoteId) {
    RequestWrapper requestWrapper = context.getCurrentRequestWrapper();
    requestWrapper.getRequest().baseUri(BLOOM_URL);
    requestWrapper.getRequest().basePath("/quotes/dialer");
    requestWrapper.getRequest().contentType("application/json");
    requestWrapper.getRequest().body("{\"uuid\": \"" + quoteId + "\"}");
    requestWrapper.setMethod(Method.POST);
    return requestWrapper;
  }

  public RequestWrapper quotesLeadListRequest(String creationDateFrom, String creationDateTo, String page, String size) {
    RequestWrapper requestWrapper = context.getCurrentRequestWrapper();
    requestWrapper.getRequest().baseUri(BLOOM_URL);
    requestWrapper.getRequest().basePath("/quotes/leadList");
    requestWrapper.getRequest().contentType("application/json");
    requestWrapper.getRequest().queryParam("creationDateFrom", creationDateFrom);
    requestWrapper.getRequest().queryParam("creationDateTo", creationDateTo);
    requestWrapper.getRequest().queryParam("page", page);
    requestWrapper.getRequest().queryParam("size", size);
    requestWrapper.setMethod(Method.GET);
    return requestWrapper;
  }

  public RequestWrapper quotesSearchByPhoneNumberRequest(String phoneNumber) {
    RequestWrapper requestWrapper = context.getCurrentRequestWrapper();
    requestWrapper.getRequest().baseUri(BLOOM_URL);
    requestWrapper.getRequest().basePath("/quotes/search-by-phonenumber");
    requestWrapper.getRequest().contentType("application/json");
    requestWrapper.getRequest().queryParam("phoneNumber", phoneNumber);
    requestWrapper.setMethod(Method.GET);
    return requestWrapper;
  }

  public RequestWrapper cbaQuotesRetrieveByQuoteNumberRequest(String quoteNumber) {
    RequestWrapper requestWrapper = context.getCurrentRequestWrapper();
    requestWrapper.getRequest().baseUri(BLOOM_URL);
    requestWrapper.getRequest().basePath("/cba/quotes");
    requestWrapper.getRequest().contentType("application/json");
    requestWrapper.getRequest().queryParam("quoteNumber", quoteNumber);
    requestWrapper.setMethod(Method.GET);
    return requestWrapper;
  }


  public RequestWrapper cbaQuotesRetrieveByPhoneNumberRequest(String quoteNumber) {
    RequestWrapper requestWrapper = context.getCurrentRequestWrapper();
    requestWrapper.getRequest().baseUri(BLOOM_URL);
    requestWrapper.getRequest().basePath("/cba/quotes");
    requestWrapper.getRequest().contentType("application/json");
    requestWrapper.getRequest().queryParam("phoneNumber", quoteNumber);
    requestWrapper.setMethod(Method.GET);
    return requestWrapper;
  }

  public RequestWrapper cbaQuotesRetrieveByClientInfoRequest(String firstName, String lastName,
      String phone, String province, String postalCode) {
    RequestWrapper requestWrapper = context.getCurrentRequestWrapper();
    requestWrapper.getRequest().baseUri(BLOOM_URL);
    requestWrapper.getRequest().basePath("cba/quotes/client");
    requestWrapper.getRequest().contentType("application/json");

    QuotesRetrieveByClientInfoRequestDTO quotesRetrieveByClientInfoRequestDTO = new QuotesRetrieveByClientInfoRequestDTO();
    quotesRetrieveByClientInfoRequestDTO.setFirstName(firstName);
    quotesRetrieveByClientInfoRequestDTO.setLastName(lastName);
    quotesRetrieveByClientInfoRequestDTO.setName(firstName + " " + lastName);
    quotesRetrieveByClientInfoRequestDTO.setPhone(phone);
    quotesRetrieveByClientInfoRequestDTO.setProvince(province);
    quotesRetrieveByClientInfoRequestDTO.setPostalCode(postalCode);
    requestWrapper.getRequest().body(mapObjectToString(quotesRetrieveByClientInfoRequestDTO));

    requestWrapper.setMethod(Method.POST);
    return requestWrapper;
  }


  public RequestWrapper cbaClientsRetrieveByName (String name) {
    RequestWrapper requestWrapper = context.getCurrentRequestWrapper();
    requestWrapper.getRequest().baseUri(BLOOM_URL);
    requestWrapper.getRequest().basePath("cba/clients");
    requestWrapper.getRequest().contentType("application/json");
    requestWrapper.getRequest().queryParam("name", name);
    requestWrapper.setMethod(Method.GET);
    return requestWrapper;
  }
}
