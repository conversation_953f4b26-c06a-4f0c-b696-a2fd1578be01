package com.intactfc.bloom.domain;

import java.util.Objects;

import jakarta.servlet.http.HttpServletRequest;

import org.owasp.esapi.ESAPI;
import org.owasp.esapi.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.context.annotation.RequestScope;


@Component
@RequestScope
public class UserAccount {
    
    static final String USERNAME_HEADER="iv-user";

    static final String UNAUTHENTICATED_USERNAME = "Unauthenticated(bloom)";

    private static final Logger log = ESAPI.getLogger(UserAccount.class);

    @Autowired
    private HttpServletRequest request;
    
    public String getUserId() {
        String userId = request.getHeader(USERNAME_HEADER);

        log.debug(Logger.EVENT_SUCCESS, "The user ID is %s".formatted(userId));

        return Objects.toString(userId, UNAUTHENTICATED_USERNAME);
    }
    
}
