import com.intact.dialer.service.DialerService;
import com.intact.dialer.service.IDialerService;
import intact.bloom.dialer.service.client.api.ApiException;
import intact.bloom.dialer.service.client.api.DialerControllerApi;
import intact.bloom.dialer.service.client.model.QuoteDialerDTO;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;

@ExtendWith(MockitoExtension.class)
class DialerServiceTest {

    @InjectMocks
    private IDialerService dialerService = new DialerService();

    @Mock
    private DialerControllerApi dialerControllerApi;


    @Test
    void test_send_sucess() throws ApiException {

        QuoteDialerDTO mockquoteDialerDTO = Mockito.mock(QuoteDialerDTO.class);
        Mockito.when(dialerControllerApi.createDialerFileUsingPOST(any(QuoteDialerDTO.class))).thenReturn("12345");

        String confirmationNumber = dialerService.send(mockquoteDialerDTO);

        assertNotNull(confirmationNumber);
        assertEquals("12345", confirmationNumber);
    }

    @Test
    void test_send_sucess_throw_apiException() throws ApiException {
        assertThrows(ApiException.class, () -> {

            QuoteDialerDTO mockquoteDialerDTO = Mockito.mock(QuoteDialerDTO.class);
            Mockito.when(dialerControllerApi.createDialerFileUsingPOST(any(QuoteDialerDTO.class))).thenThrow(new ApiException());

            String confirmationNumber = dialerService.send(mockquoteDialerDTO);

            assertNull(confirmationNumber);
        });
    }

}
