package com.intactfc.bloom.controller;

import com.intactfc.bloom.domain.DialerDTO;
import com.intactfc.bloom.domain.DialerResponseDTO;
import com.intactfc.bloom.domain.PageUIDTO;
import com.intactfc.bloom.domain.QuoteUIDTO;
import com.intactfc.bloom.facade.DialerFacade;
import com.intactfc.bloom.facade.DialerFileCreatorFacade;
import intact.bloom.api.client.api.ApiException;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.owasp.esapi.ESAPI;
import org.owasp.esapi.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.Valid;
import java.util.Collection;

@CrossOrigin(origins = "*", maxAge = 3600)
@RestController
@RequestMapping("/quotes")
public class DialerFileCreatorController {

    private static final Logger log = ESAPI.getLogger(DialerFileCreatorController.class);

    @Autowired
    private DialerFileCreatorFacade dialerFileCreatorFacade;

    @Autowired
    private DialerFacade dialerFacade;

    @GetMapping("/search-by-phonenumber")
    public ResponseEntity<Collection<QuoteUIDTO>> searchQuotesByPhoneNumber(@RequestParam String phoneNumber) {

        if (StringUtils.isBlank(phoneNumber)) {
            return new ResponseEntity("phoneNumber can't be empty", HttpStatus.BAD_REQUEST);
        }

        Collection<QuoteUIDTO> results = this.dialerFileCreatorFacade.getQuotesForReview(phoneNumber);

        if(CollectionUtils.isEmpty(results))
            return new ResponseEntity<>(HttpStatus.NO_CONTENT);

        return new ResponseEntity<>(results, HttpStatus.OK);
    }

    @PutMapping
    public ResponseEntity<QuoteUIDTO> update(@Valid @RequestBody QuoteUIDTO entryQuote) {
        return new ResponseEntity<>(this.dialerFileCreatorFacade.partialUpdateQuote(entryQuote), HttpStatus.OK);

    }

    @PostMapping("/dialer")
    public  ResponseEntity<DialerResponseDTO> send(@RequestBody DialerDTO dialerDTO){

        DialerResponseDTO dialerResponseDTO = dialerFacade.sendToDialer(dialerDTO);

        return dialerResponseDTO != null && dialerResponseDTO.getNewLinkedQuotesUIDTO() != null ?
            new ResponseEntity<>(dialerResponseDTO, HttpStatus.CONFLICT) :
            new ResponseEntity<>(dialerResponseDTO, HttpStatus.OK);
    }

    @GetMapping(value="/leadList")
    public ResponseEntity<PageUIDTO> searchLeadListQuotes(@RequestParam String creationDateFrom, @RequestParam String creationDateTo, @RequestParam(required=false) String province, Integer page, Integer size) throws ApiException {

        log.info(Logger.EVENT_SUCCESS, "Searching quotes from %s to %s for %s".formatted(creationDateFrom, creationDateTo, province));

        PageUIDTO pageToBeReturned = this.dialerFileCreatorFacade.searchLeadListQuotes(creationDateFrom,creationDateTo,province,page,size);

        log.info(Logger.EVENT_SUCCESS, "Successfully searched quotes from %s to %s for %s".formatted(creationDateFrom, creationDateTo, province));

        return new ResponseEntity<>(pageToBeReturned, HttpStatus.OK);
    }
}
