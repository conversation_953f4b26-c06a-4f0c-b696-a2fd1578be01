package com.intactfc.bloom.enums;

import java.util.Arrays;

public enum AgreementFollowUpStatusEnum {
    NOT_CONT("NOT CONTACTED"),
    CONT_BUT_FLW("CONTACT, FOLLOW-UP NEEDED"),
    CONT_BUT_NO_FLW("CONTACT, NO FOLLOW-UP NEEDED"),
    NOT_CONT_NO_FLW("NOT CONTACTED, NO FOLLOWUP NEEDED"),
    CUST_CALLBK_REQ("CALLBACK REQUESTED"),
    DISCARD("DUPLICATE/FAKE");

    private String description;

    private AgreementFollowUpStatusEnum(String aDescription) {
        this.description = aDescription;
    }

    public String getDescription() {
        return this.description;
    }

    public AgreementFollowUpStatusEnum fromValue(String value) {

        AgreementFollowUpStatusEnum agreementFollowUpStatusEnum = Arrays.stream(AgreementFollowUpStatusEnum.values()).filter(
                followUpStatusEnum -> followUpStatusEnum.name().equals(value))
                .findFirst()
                .orElse(null);

        return agreementFollowUpStatusEnum;
    }

}
