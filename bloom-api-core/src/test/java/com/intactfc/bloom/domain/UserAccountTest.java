package com.intactfc.bloom.domain;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import jakarta.servlet.http.HttpServletRequest;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class UserAccountTest {
    
    @InjectMocks
    private UserAccount userAccount = new UserAccount();
    
    @Mock
    private HttpServletRequest request;

    @Test
    void getId_authenticated() {
        when(request.getHeader(UserAccount.USERNAME_HEADER)).thenReturn("test");
        assertEquals("test",userAccount.getUserId());
        verify(request).getHeader(UserAccount.USERNAME_HEADER);
    }

    @Test
    void getId_not_authenticated() {
        when(request.getHeader(UserAccount.USERNAME_HEADER)).thenReturn(null);
        assertEquals(UserAccount.UNAUTHENTICATED_USERNAME,userAccount.getUserId());
        verify(request).getHeader(UserAccount.USERNAME_HEADER);
    }
    
}
