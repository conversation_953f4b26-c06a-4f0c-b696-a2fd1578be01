package intact.test.bloom.integration.glue;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import intact.test.bloom.integration.context.TestContext;
import intact.test.bloom.integration.models.ClientsResponseDTO;
import intact.test.bloom.integration.models.QuoteLeadListResponseDTO;
import intact.test.bloom.integration.models.QuoteLeadResponseDTO;
import intact.test.bloom.integration.models.QuoteListFromSearchResponseDTO;
import intact.test.integration.glue.steps.AbstractValidationSteps;
import io.cucumber.java.en.Then;
import io.cucumber.java.en.When;
import io.restassured.response.Response;
import org.hamcrest.Matchers;

public class AssertionSteps extends AbstractValidationSteps<TestContext> {

  public AssertionSteps(TestContext testContext) {
    super(testContext);
  }

  @When("list of leads contains records")
  public void theUserRetrievesListOfLeads() {
    QuoteLeadListResponseDTO quoteLeadResponseDTO = context.getLastResponseDTO(
        QuoteLeadListResponseDTO.class);
    assertWithMessage("totalElements expected more than 0",
        quoteLeadResponseDTO.getTotalElements() > 0);
    assertWithMessage("No records returned, expected more than 0",
        quoteLeadResponseDTO.getContent().size() > 0);
  }

  @When("number of quotes returned {int} or more")
  public void numberOfQuotesReturnedOrMore(Integer numberOfQuotes) {
    QuoteListFromSearchResponseDTO quoteListFromSearchResponseDTO = getQuoteListFromSearchResponseDTO(
        context.getLastResponse().body().asString());
    int totalQuotes = quoteListFromSearchResponseDTO.size();
    assertWithMessage("No records returned, expected: " + numberOfQuotes + " or more",
        totalQuotes >= numberOfQuotes);
  }

  public static QuoteListFromSearchResponseDTO getQuoteListFromSearchResponseDTO(
      String jsonString) {
    try {
      return new ObjectMapper().readValue(jsonString, QuoteListFromSearchResponseDTO.class);
    } catch (JsonProcessingException e) {
      throw new RuntimeException(e);
    }
  }

  @When("number of cba quotes returned {int}")
  public void numberOfQuotesReturned(Integer numberOfQuotes) {
    QuoteLeadResponseDTO quoteLeadResponseDTO = context.getLastResponseDTO(
        QuoteLeadResponseDTO.class);
    int totalQuotes =
        quoteLeadResponseDTO.getMainQuotes().size() + quoteLeadResponseDTO.getLinkedQuotes().size()
            + quoteLeadResponseDTO.getOtherQuotes().size();
    assertWithMessage("No records returned, expected: " + numberOfQuotes,
        totalQuotes == numberOfQuotes);
  }

  @When("number of cba quotes returned {int} or more")
  public void numberOfCbaQuotesReturnedOrMore(Integer numberOfQuotes) {
    QuoteLeadResponseDTO quoteLeadResponseDTO = context.getLastResponseDTO(
        QuoteLeadResponseDTO.class);
    int totalQuotes =
        quoteLeadResponseDTO.getMainQuotes().size() + quoteLeadResponseDTO.getLinkedQuotes().size()
            + quoteLeadResponseDTO.getOtherQuotes().size();
    assertWithMessage("No records returned, expected: " + numberOfQuotes + " or more",
        totalQuotes >= numberOfQuotes);
  }

  @When("number of cba clients returned {int} or more")
  public void numberOfCbaClientsReturnedOrMore(Integer numberOfClients) {
    ClientsResponseDTO clientsResponseDTO = context.getLastResponseDTO(ClientsResponseDTO.class);
    int totalClients = clientsResponseDTO.getClients().size();
    assertWithMessage("No records returned, expected: " + numberOfClients + " or more",
        totalClients >= numberOfClients);
  }

  @Then("Bloom HTTP response code should be {int}")
  public void bloomHTTPResponseCodeShouldBe(int httpResponseCode) {
    Response response = this.context.getLastResponse();
    int statusCode = response.statusCode();
    this.assertWithMessage("Incorrect HTTP status in response", statusCode, Matchers.equalTo(httpResponseCode));
  }
}
