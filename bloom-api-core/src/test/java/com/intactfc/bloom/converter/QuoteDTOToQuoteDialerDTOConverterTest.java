package com.intactfc.bloom.converter;

import com.intactfc.bloom.mock.quote.MockQuoteDTO;
import intact.bloom.api.client.model.QuoteDTO;
import intact.bloom.dialer.service.client.model.QuoteDialerDTO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.core.convert.converter.Converter;

import static org.junit.jupiter.api.Assertions.*;

@ExtendWith(MockitoExtension.class)
class QuoteDTOToQuoteDialerDTOConverterTest {

    private QuoteDTO quoteDTOMock;

    @BeforeEach
    void setUp() {
        quoteDTOMock = MockQuoteDTO.createQuoteDTO();
    }

    @InjectMocks
    private Converter<QuoteDTO, QuoteDialerDTO>  quoteDTOToQuoteDialerDTOConverter = new QuoteDTOToQuoteDialerDTOConverter();

    @Test
    void test_convert_null_quoteDTO_return_null() {

        QuoteDialerDTO quoteDialerDTO = quoteDTOToQuoteDialerDTOConverter.convert(null);

        assertNull(quoteDialerDTO);
    }

    @Test
    void test_convert_success() {

        QuoteDialerDTO quoteDialerDTO = quoteDTOToQuoteDialerDTOConverter.convert(quoteDTOMock);

        assertNotNull(quoteDialerDTO);
        assertEquals("QF123456798",quoteDialerDTO.getQuoteNumber());
        assertEquals("HOME_QUICKQUOTE",quoteDialerDTO.getQuoteAppEnum().getValue());
        assertEquals("PERSONAL",quoteDialerDTO.getLineofBusiness().getValue());
        assertEquals("INTACT_QUEBEC",quoteDialerDTO.getSourceUnderwritingCompany().getValue());
        assertEquals("QUOTE_COMPLETE",quoteDialerDTO.getQuoteStatus().getValue());

    }

    @Test
    void test_convert_quoteDTO_with_null_QuoteAppEnumEnum() {

        quoteDTOMock.setQuoteAppEnum(null);

        QuoteDialerDTO quoteDialerDTO = quoteDTOToQuoteDialerDTOConverter.convert(quoteDTOMock);

        assertNotNull(quoteDialerDTO);
        assertEquals("QF123456798",quoteDialerDTO.getQuoteNumber());
        assertNull(quoteDialerDTO.getQuoteAppEnum());
    }

    @Test
    void test_convert_quoteDTO_with_null_sourceUnderwritingCompanyEnum() {

        quoteDTOMock.setSourceUnderwritingCompany(null);

        QuoteDialerDTO quoteDialerDTO = quoteDTOToQuoteDialerDTOConverter.convert(quoteDTOMock);

        assertNotNull(quoteDialerDTO);
        assertEquals("QF123456798",quoteDialerDTO.getQuoteNumber());
        assertNull(quoteDialerDTO.getSourceUnderwritingCompany());
    }

    @Test
    void test_convert_quoteDTO_with_null_vehiculesDTO() {

        quoteDTOMock.setListVehiclesDTO(null);

        QuoteDialerDTO quoteDialerDTO = quoteDTOToQuoteDialerDTOConverter.convert(quoteDTOMock);

        assertNotNull(quoteDialerDTO);
        assertTrue(quoteDialerDTO.getListVehiclesDTO().isEmpty());
    }

    @Test
    void test_convert_quoteDTO_with_null_lineOfBusiness() {

        quoteDTOMock.setLineofBusiness(null);

        QuoteDialerDTO quoteDialerDTO = quoteDTOToQuoteDialerDTOConverter.convert(quoteDTOMock);

        assertNotNull(quoteDialerDTO);
        assertNull(quoteDialerDTO.getLineofBusiness());
    }

    @Test
    void test_convert_quoteDTO_with_null_quoteStatus() {

        quoteDTOMock.setQuoteStatus(null);

        QuoteDialerDTO quoteDialerDTO = quoteDTOToQuoteDialerDTOConverter.convert(quoteDTOMock);

        assertNotNull(quoteDialerDTO);
        assertNull(quoteDialerDTO.getQuoteStatus());
    }
}
