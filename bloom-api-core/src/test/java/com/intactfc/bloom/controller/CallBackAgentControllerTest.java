package com.intactfc.bloom.controller;

import com.intactfc.bloom.domain.QuoteUIDTO;
import com.intactfc.bloom.domain.SearchClientResultDTO;
import com.intactfc.bloom.domain.SearchQuoteResult;
import com.intactfc.bloom.exceptions.QuoteNotFoundException;
import com.intactfc.bloom.exceptions.handlers.CustomRestExceptionHandler;
import com.intactfc.bloom.facade.CallBackAgentFacade;
import com.intactfc.bloom.facade.ClientDTO;
import com.intactfc.bloom.mock.quote.MockQuoteUIDTO;
import intact.bloom.api.client.model.QuoteDTO;

import static com.intactfc.bloom.controller.DialerFileCreatorControllerTest.asJsonString;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import java.util.LinkedList;
import java.util.List;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

@ExtendWith(MockitoExtension.class)
class CallBackAgentControllerTest {

    @InjectMocks
    private CallBackAgentController callBackAgentController = new CallBackAgentController();

    @Mock
    private CallBackAgentFacade callBackAgentFacade;
    
    private MockMvc mockMvc;

    private ClientDTO clientDTO;

    @BeforeEach
    void setUp() {
        mockMvc = MockMvcBuilders.standaloneSetup(callBackAgentController)
                .setControllerAdvice(new CustomRestExceptionHandler())
                .build();
    }

    @Test
    void test_searchQuoteByWorkFlowStatusReturnNoContent() throws Exception {

        Mockito.when(callBackAgentFacade.searchQuotesByWorkFlowStatus(anyString())).thenReturn(null);

        mockMvc.perform(
                get("/cba/workFlowStatus/{workFlowStatus}","test")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isNoContent());
    }

    @Test
    void test_searchQuoteByWorkFlowStatusReturnOK() throws Exception {

        List<QuoteDTO> listQuoteDTO = new LinkedList<>();
        QuoteDTO quoteDTO = new QuoteDTO();
        listQuoteDTO.add(quoteDTO);

        Mockito.when(callBackAgentFacade.searchQuotesByWorkFlowStatus(anyString())).thenReturn(listQuoteDTO);

        mockMvc.perform(get("/cba/workFlowStatus/{workFlowStatus}","test")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @Test
    void test_searchQuoteByQuoteNumberReturnNoContent() throws Exception {
        Mockito.when(callBackAgentFacade.searchAllRelatedToQuoteNumber(anyString())).thenThrow(new QuoteNotFoundException("The quote with the quoteNumber "));

        mockMvc.perform(get("/cba/quotes")
                .param("quoteNumber","test")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isNoContent());
    }

    @Test
    void test_searchQuoteByQuoteNumberReturnOK() throws Exception {

        QuoteUIDTO testQuote = MockQuoteUIDTO.createQuoteUIDTO();
        SearchQuoteResult result = new SearchQuoteResult();
        result.getMainQuotes().add(testQuote);
        Mockito.when(callBackAgentFacade.searchAllRelatedToQuoteNumber(anyString())).thenReturn(result);

        mockMvc.perform(get("/cba/quotes")
                .param("quoteNumber","test"))
                .andExpect(status().isOk());
    }

    @Test
    void test_searchByQuoteNumberReturnBadRequest() throws Exception {
        mockMvc.perform(get("/cba/quotes")
                .param("quoteNumber"," ")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isBadRequest());
    }

    @Test
    void searchQuotesByPhoneNumber_oneResult() throws Exception {
        SearchQuoteResult facadeResult = new SearchQuoteResult();
        facadeResult.getMainQuotes().add(new QuoteUIDTO());
        facadeResult.getLinkedQuotes().add(new QuoteUIDTO());
        facadeResult.getOtherQuotes().add(new QuoteUIDTO());

        Mockito.when(callBackAgentFacade.searchQuotesByPhoneNumber(anyString())).thenReturn(facadeResult);

        mockMvc.perform(get("/cba/quotes")
                .param("phoneNumber","9999999999")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.mainQuotes[0]").exists())
                .andExpect(jsonPath("$.linkedQuotes[0]").exists())
                .andExpect(jsonPath("$.otherQuotes[0]").exists());

        Mockito.verify(callBackAgentFacade).searchQuotesByPhoneNumber(eq("9999999999"));
    }

    @Test
    void searchQuotesByPhoneNumber_noResult() throws Exception {

        Mockito.when(callBackAgentFacade.searchQuotesByPhoneNumber(anyString())).thenReturn(new SearchQuoteResult());

        mockMvc.perform(get("/cba/quotes")
                .param("phoneNumber","9999999999")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.mainQuotes[0]").doesNotExist())
                .andExpect(jsonPath("$.linkedQuotes[0]").doesNotExist())
                .andExpect(jsonPath("$.otherQuotes[0]").doesNotExist());

        Mockito.verify(callBackAgentFacade).searchQuotesByPhoneNumber(eq("9999999999"));
    }

    @Test
    void searchClientsByName_oneResult() throws Exception {
        SearchClientResultDTO result = new SearchClientResultDTO();
        ClientDTO client = new ClientDTO();
        client.setName("One Result !");
        result.getClients().add(client);

        Mockito.when(callBackAgentFacade.searchClientsByName(anyString())).thenReturn(result);

        mockMvc.perform(get("/cba/clients")
                .param("name","DummyName")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.clients").exists())
                .andExpect(jsonPath("$.clients[0]").exists())
                .andExpect(jsonPath("$.clients[0].name").value("One Result !"));

        Mockito.verify(callBackAgentFacade).searchClientsByName(eq("DummyName"));
    }

    @Test
    void searchQuotesByClient_oneResult() throws Exception {

        clientDTO = new ClientDTO();

        clientDTO.setFirstName("Crocodile");
        clientDTO.setLastName("Dundee");
        clientDTO.setPhone("9999999999");
        clientDTO.setPostalCode("H0H0H0");

        SearchQuoteResult facadeResult = new SearchQuoteResult();
        facadeResult.getMainQuotes().add(new QuoteUIDTO());
        facadeResult.getLinkedQuotes().add(new QuoteUIDTO());
        facadeResult.getOtherQuotes().add(new QuoteUIDTO());

        Mockito.when(callBackAgentFacade.searchQuotesByClient(
                any(ClientDTO.class))).thenReturn(facadeResult);

        mockMvc.perform(post("/cba/quotes/client")
                .content(asJsonString(clientDTO))
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.mainQuotes[0]").exists())
                .andExpect(jsonPath("$.linkedQuotes[0]").exists())
                .andExpect(jsonPath("$.otherQuotes[0]").exists());

        Mockito.verify(callBackAgentFacade).searchQuotesByClient(eq(clientDTO));
    }

    @Test
    void searchQuotesByClient_noResult() throws Exception {

        clientDTO = new ClientDTO();

        clientDTO.setFirstName("Crocodile");
        clientDTO.setLastName("Dundee");
        clientDTO.setPhone("9999999999");
        clientDTO.setPostalCode("H0H0H0");

        Mockito.when(callBackAgentFacade.searchQuotesByClient(
                any(ClientDTO.class))).thenReturn(new SearchQuoteResult());

        mockMvc.perform(post("/cba/quotes/client")
                .content(asJsonString(clientDTO))
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.mainQuotes[0]").doesNotExist())
                .andExpect(jsonPath("$.linkedQuotes[0]").doesNotExist())
                .andExpect(jsonPath("$.otherQuotes[0]").doesNotExist());

        Mockito.verify(callBackAgentFacade).searchQuotesByClient(eq(clientDTO));
    }
}
