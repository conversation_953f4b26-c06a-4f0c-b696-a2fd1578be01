package com.intactfc.bloom.converter;

import intact.bloom.api.client.model.QuoteDTO;
import intact.bloom.dialer.service.client.model.PartyDTO;
import intact.bloom.dialer.service.client.model.PhoneDTO;
import intact.bloom.dialer.service.client.model.QuoteDialerDTO;
import intact.bloom.dialer.service.client.model.VehicleDTO;
import org.owasp.esapi.ESAPI;
import org.owasp.esapi.Logger;
import org.springframework.core.convert.converter.Converter;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;


@Component
public class QuoteDTOToQuoteDialerDTOConverter implements Converter<QuoteDTO, QuoteDialerDTO> {

    private static final Logger log = ESAPI.getLogger(QuoteDTOToQuoteDialerDTOConverter.class);

    @Override
    public QuoteDialerDTO convert(QuoteDTO quoteDTO) {

        if(quoteDTO == null) {
            return null;
        }

        QuoteDialerDTO quoteDialerDTO = new QuoteDialerDTO();

        quoteDialerDTO.setId(quoteDTO.getId());
        quoteDialerDTO.setCreationTimeStamp(quoteDTO.getCreationTimeStamp());
        quoteDialerDTO.setDatasourceOrigin(convertToDatasourceOrigin(quoteDTO.getDatasourceOrigin()));
        quoteDialerDTO.setDistributorNumber(quoteDTO.getBroker() != null ? quoteDTO.getBroker().getNumber() : null);
        quoteDialerDTO.setParty(convertToPartyDTO(quoteDTO.getParty()));
        quoteDialerDTO.setPhone(converToPhoneDTO(quoteDTO.getPhone()));
        quoteDialerDTO.setLineofBusiness(getLineOfBusiness(quoteDTO.getLineofBusiness()));
        quoteDialerDTO.setListVehiclesDTO(getVehiclesDTO(quoteDTO.getListVehiclesDTO()));
        quoteDialerDTO.setQuoteAppEnum(getQuoteAppEnum(quoteDTO.getQuoteAppEnum()));
        quoteDialerDTO.setQuoteSource(getQuoteSourceEnum(quoteDTO.getQuoteSource()));
        quoteDialerDTO.setQuoteNumber(quoteDTO.getQuoteNumber());
        quoteDialerDTO.setQuoteStatus(convertToQuoteStatus(quoteDTO.getQuoteStatus()));
        quoteDialerDTO.setSourceUnderwritingCompany(getSourceUnderwritingCompanyEnum(quoteDTO.getSourceUnderwritingCompany()));
        quoteDialerDTO.setInceptionDate(quoteDTO.getInceptionDate());

        return  quoteDialerDTO;
    }

    private QuoteDialerDTO.DatasourceOriginEnum convertToDatasourceOrigin(QuoteDTO.DatasourceOriginEnum datasourceOriginEnum) {
        return datasourceOriginEnum == null ? null :
                QuoteDialerDTO.DatasourceOriginEnum.fromValue(datasourceOriginEnum.getValue());
    }

    private PartyDTO convertToPartyDTO(intact.bloom.api.client.model.PartyDTO party) {
        PartyDTO partyDTO = new PartyDTO();

        partyDTO.setFirstName(party.getFirstName());
        partyDTO.setLastName(party.getLastName());
        partyDTO.setCompanyName(party.getCompanyName());
        partyDTO.setProvince(party.getAddress() != null ? party.getAddress().getProvince() : null);

        return partyDTO;
    }

    private PhoneDTO converToPhoneDTO(intact.bloom.api.client.model.PhoneDTO phone) {
        PhoneDTO phoneDTO = new PhoneDTO();

        phoneDTO.setAreaCode(phone.getAreaCode());
        phoneDTO.setExtension(phone.getExtension());
        phoneDTO.setPhoneNumber(phone.getPhoneNumber());

        return phoneDTO;
    }

    private QuoteDialerDTO.LineofBusinessEnum getLineOfBusiness(QuoteDTO.LineofBusinessEnum lineofBusiness) {
        if(lineofBusiness == null) {
            log.error(Logger.EVENT_FAILURE, "lineOfBusiness is null in quoteDTO");
            return null;
        }

        return QuoteDialerDTO.LineofBusinessEnum.fromValue(lineofBusiness.getValue());
    }

    private List<VehicleDTO> getVehiclesDTO(List<intact.bloom.api.client.model.VehicleDTO> listVehiclesDTO) {
        if(CollectionUtils.isEmpty(listVehiclesDTO)) {
            return Collections.emptyList();
        }

        List<VehicleDTO> quoteDialerVehicleDTOS = new ArrayList<>();
        VehicleDTO quoteDialerVehicleDTO;

       for(intact.bloom.api.client.model.VehicleDTO vehicleDTO : listVehiclesDTO) {
           quoteDialerVehicleDTO = new VehicleDTO();
           quoteDialerVehicleDTO.setMake(vehicleDTO.getMake());
           quoteDialerVehicleDTO.setModel(vehicleDTO.getModel());
           quoteDialerVehicleDTO.setYear(vehicleDTO.getYear());

           quoteDialerVehicleDTOS.add(quoteDialerVehicleDTO);
       }

       return quoteDialerVehicleDTOS;
    }

    private QuoteDialerDTO.QuoteAppEnumEnum getQuoteAppEnum(QuoteDTO.QuoteAppEnumEnum quoteAppEnum) {

        if (quoteAppEnum == null ) {
            return null;
        }

        return QuoteDialerDTO.QuoteAppEnumEnum.fromValue(quoteAppEnum.getValue());
    }

    private QuoteDialerDTO.QuoteSourceEnum getQuoteSourceEnum(QuoteDTO.QuoteSourceEnum quoteSourceEnum) {

        if (quoteSourceEnum == null ) {
            return null;
        }

        return QuoteDialerDTO.QuoteSourceEnum.fromValue(quoteSourceEnum.getValue());
    }

    private QuoteDialerDTO.SourceUnderwritingCompanyEnum getSourceUnderwritingCompanyEnum(QuoteDTO.SourceUnderwritingCompanyEnum sourceUnderwritingCompanyEnum) {
        if(sourceUnderwritingCompanyEnum == null) {
            return null;
        }

        return QuoteDialerDTO.SourceUnderwritingCompanyEnum.fromValue(sourceUnderwritingCompanyEnum.getValue());
    }

    private QuoteDialerDTO.QuoteStatusEnum convertToQuoteStatus(QuoteDTO.QuoteStatusEnum quoteStatusEnum) {
        if(quoteStatusEnum == null) {
            return null;
        }

        return QuoteDialerDTO.QuoteStatusEnum.fromValue(quoteStatusEnum.getValue());
    }

}
