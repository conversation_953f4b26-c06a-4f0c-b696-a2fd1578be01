package com.intactfc.bloom.domain;

import intact.bloom.api.client.model.AddressDTO;
import intact.bloom.api.client.model.BrokerDTO;
import intact.bloom.api.client.model.QuoteDTO.WorkFlowStatusEnum;
import intact.bloom.api.client.model.VehicleDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import jakarta.validation.constraints.NotBlank;
import java.util.List;

@ToString
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class QuoteUIDTO {

    @NotBlank(message = "The id can't be null or empty.")
    @ApiModelProperty(notes = "The database generated product ID")
    private String id;

    @ApiModelProperty(notes = "The Policy Holder complete name", required = true)
    private String name;

    @NotBlank(message = "The phoneNumber can't be null or empty.")
    @ApiModelProperty(notes = "Policy Holder Phone Number")
    private String phoneNumber;

    @ApiModelProperty(notes = "The quote Creation Time Stamp. Format : yyyy-MM-ddHH:mm:ss ", required = true)
    private String date;

    private AddressDTO address;

    @ApiModelProperty(notes = "Flag to hide quote in UI")
    private Boolean isHidden;

    @ApiModelProperty(notes = "Flag to tell whatever the quote is incomplete or not")
    private Boolean isQuoteIncomplete;

    @ApiModelProperty(notes = "Flag to tell whatever the quote is complete or not")
    private Boolean isQuoteComplete;

    @ApiModelProperty(notes = "Flag to tell whatever the quote is roadblock or not")
    private Boolean isQuoteRoadBlock;

    @ApiModelProperty(notes = "Flag to tell whatever the quote is roadblock with existing client or not")
    private Boolean isQuoteAlReadyClient;

    @ApiModelProperty(notes = "Flag to tell whatever the quote is to be reviewed")
    private Boolean isQuoteToBeReviewed;

    @ApiModelProperty(notes = "Indicate the status of the quote.")
    private WorkFlowStatusEnum workflowStatus;
    
    private String creationDateTime;
    private List<VehicleDTO> vehicles;
    private String quoteNumber;
    private BrokerDTO broker;
}