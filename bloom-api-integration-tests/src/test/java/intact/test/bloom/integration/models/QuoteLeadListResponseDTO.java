package intact.test.bloom.integration.models;

import intact.test.integration.models.dto.AbstractResponseDTO;
import intact.test.integration.models.dto.ErrorDTO;
import java.util.ArrayList;
import java.util.List;
import lombok.Data;

@Data
public class QuoteLeadListResponseDTO extends AbstractResponseDTO<List<QuoteLeadDTO>, ErrorDTO> {
  private List<QuoteLeadDTO> content = new ArrayList<>();
  private Integer totalElements;
  private Integer size;
  private Integer number;

    @Override
    public boolean hasNullData() {
      return this.getData() == null;
    }
}
