package com.intactfc.bloom.mock.quote;

import com.intactfc.bloom.domain.QuoteUIDTO;

public class MockQuoteUIDTO {


    public static QuoteUIDTO createQuoteUIDTO() {
        QuoteUIDTO quoteUIDTO  = new QuoteUIDTO();

        quoteUIDTO.setId("12");
        quoteUIDTO.setIsHidden(true);
        quoteUIDTO.setDate("2019-08-10 12:00:00");
        quoteUIDTO.setPhoneNumber("************");
        quoteUIDTO.setName("Bob Burgington");
        return quoteUIDTO;
    }
}
