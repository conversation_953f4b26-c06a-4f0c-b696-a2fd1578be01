package com.intactfc.bloom.mock.quote;

import intact.bloom.api.client.model.PhoneDTO;

public class MockPhoneDTO {
    public static PhoneDTO createPhoneDTO() {
        PhoneDTO phoneDTO = new PhoneDTO();

        phoneDTO.setAreaCode("514");
        phoneDTO.setPhoneNumber("4567894");
        phoneDTO.setExtension("1234");
        phoneDTO.setPhoneType(PhoneDTO.PhoneTypeEnum.BUSINESS);

        return phoneDTO;
    }
}
