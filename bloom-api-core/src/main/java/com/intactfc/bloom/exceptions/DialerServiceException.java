package com.intactfc.bloom.exceptions;

import lombok.Data;

@Data
public class DialerServiceException extends RuntimeException {
    private int statusCode;
    
    public DialerServiceException(String message) {
    	super(message);
    }

    public DialerServiceException(String message, Throwable throwable) {
        super(message, throwable);
    }
    
    public DialerServiceException(String message, int statusCode) {
        super(message);
        this.statusCode = statusCode;
    }

    public DialerServiceException(String message, int statusCode, Throwable throwable) {
        super(message, throwable);
        this.statusCode = statusCode;
    }
}
