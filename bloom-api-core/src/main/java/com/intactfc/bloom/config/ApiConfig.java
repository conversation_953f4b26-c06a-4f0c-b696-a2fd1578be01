package com.intactfc.bloom.config;

import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Info;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class ApiConfig {

    private static final String API_TITLE = "Bloom API";
    private static final String API_VERSION = "3.0.5";

  @Bean
  OpenAPI openApi() {
    return new OpenAPI().info(new Info().title(API_TITLE)
        .version("Documentation for the Bloom API's REST endpoints")
        .version(API_VERSION));
  }


  @Bean
  public FilterRegistrationBean<HstsFilter> hstsFilter() {
    FilterRegistrationBean<HstsFilter> registrationBean = new FilterRegistrationBean<>();
    registrationBean.setFilter(new HstsFilter());
    registrationBean.addUrlPatterns("/*");
    return registrationBean;
  }
}
