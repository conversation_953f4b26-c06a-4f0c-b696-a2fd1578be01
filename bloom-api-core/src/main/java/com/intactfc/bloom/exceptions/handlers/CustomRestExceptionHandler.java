package com.intactfc.bloom.exceptions.handlers;

import com.intactfc.bloom.domain.ErrorUIDTO;
import com.intactfc.bloom.exceptions.BloomRepositoryServiceException;
import com.intactfc.bloom.exceptions.DialerServiceException;
import com.intactfc.bloom.exceptions.QuoteNotFoundException;
import io.swagger.annotations.Api;
import lombok.AllArgsConstructor;
import org.owasp.esapi.ESAPI;
import org.owasp.esapi.Logger;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.context.request.WebRequest;

import jakarta.validation.ValidationException;

@AllArgsConstructor
@ControllerAdvice
@Api
public class CustomRestExceptionHandler {

    private static Logger log = ESAPI.getLogger(CustomRestExceptionHandler.class);

    @ExceptionHandler(ValidationException.class)
    public ResponseEntity<Object> handleValidationException(ValidationException ex, WebRequest request) {
        return new ResponseEntity<>(
                createErrorUIDTO(HttpStatus.BAD_REQUEST.value(), ex.getMessage()),
                HttpStatus.BAD_REQUEST);
    }

    @ExceptionHandler(DialerServiceException.class)
    public ResponseEntity<Object> handleDialerServiceException(DialerServiceException ex) {
        log.error(Logger.EVENT_FAILURE, "Unhandled Dialer service error.", ex);
        return new ResponseEntity<>(
            createErrorUIDTO(HttpStatus.INTERNAL_SERVER_ERROR.value(), ex.getMessage()),
            HttpStatus.INTERNAL_SERVER_ERROR);
    }

    @ExceptionHandler(Exception.class)
    public ResponseEntity<Object> handleException(Exception ex) {
        log.error(Logger.EVENT_FAILURE, "Unhandled exception while processing request", ex);
        return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
    }


    @ExceptionHandler(BloomRepositoryServiceException.class)
    public ResponseEntity<ErrorUIDTO> handleBloomRepoServiceException(BloomRepositoryServiceException ex) {
        log.error(Logger.EVENT_FAILURE, "Unhandled exception while woring with the repository service.", ex);
        return new ResponseEntity<>(
                createErrorUIDTO(HttpStatus.INTERNAL_SERVER_ERROR.value(), ex.getMessage()),
                HttpStatus.INTERNAL_SERVER_ERROR);
    }

    @ExceptionHandler(QuoteNotFoundException.class)
    public ResponseEntity<ErrorUIDTO> handleQuoteNotFoundException(QuoteNotFoundException ex) {
        log.warning(Logger.EVENT_FAILURE, "Unhandled QuoteNotFoundException while processing request", ex);
        return new ResponseEntity<>(
                createErrorUIDTO(HttpStatus.NO_CONTENT.value(), ex.getMessage()),
                HttpStatus.NO_CONTENT);
    }

    @ExceptionHandler(MethodArgumentNotValidException.class)
    public ResponseEntity<ErrorUIDTO> handleMissingServletRequestParameterException(MethodArgumentNotValidException ex) {

        return new ResponseEntity<>(
                createErrorUIDTO(HttpStatus.BAD_REQUEST.value(), ex.getMessage()),
                HttpStatus.BAD_REQUEST);
    }

    public ErrorUIDTO createErrorUIDTO(int statusCode, String message) {
        ErrorUIDTO errorUIDTO = new ErrorUIDTO();
        errorUIDTO.getErrors().add(
                ErrorUIDTO.Details.builder()
                        .code(String.valueOf(statusCode))
                        .message(message)
                        .build()
        );

        return errorUIDTO;
    }

}
