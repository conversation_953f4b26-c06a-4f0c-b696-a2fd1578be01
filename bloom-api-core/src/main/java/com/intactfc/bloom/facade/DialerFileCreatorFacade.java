package com.intactfc.bloom.facade;

import com.intactfc.bloom.converter.PageToPageUIDTOConverter;
import com.intactfc.bloom.converter.QuoteDTOToQuoteUIDTOConverter;
import com.intactfc.bloom.domain.FollowUpMessage;
import com.intactfc.bloom.domain.PageUIDTO;
import com.intactfc.bloom.domain.QuoteUIDTO;
import com.intactfc.bloom.domain.UserAccount;
import com.intactfc.bloom.exceptions.BloomRepositoryServiceException;
import com.intactfc.bloom.service.BloomRepositoryClient;
import com.intactfc.bloom.utils.FollowUpMessageHelper;
import intact.bloom.api.client.api.ApiException;
import intact.bloom.api.client.model.QuoteDTO;
import intact.bloom.api.client.model.QuoteDTO.WorkFlowStatusEnum;
import intact.bloom.dialer.service.client.model.QuoteDialerDTO;
import java.util.Collections;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.owasp.esapi.ESAPI;
import org.owasp.esapi.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.convert.converter.Converter;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.util.List;
import java.util.stream.Collectors;

@Component
public class DialerFileCreatorFacade {

    @Autowired
    private QuoteDTOToQuoteUIDTOConverter converter;

    @Autowired
    private PageToPageUIDTOConverter pageToPageUIDTOConverter;

    private static final Logger log = ESAPI.getLogger(DialerFileCreatorFacade.class);

    @Autowired
    private BloomRepositoryClient bloomRepositoryClient;

    @Autowired
    public Converter<QuoteDTO, QuoteDialerDTO> quoteDTOToQuoteDialerDTOConverter;

    @Autowired
    RestTemplate restTemplate;
    
    @Autowired
    private UserAccount userAccount;

    @Value("${bloom-plp-processor.url.update}")
    private String bloomPlpUrl;

    @Value("${bloom-xpas-processor.url.update}")
    private String bloomXpasUrl;

    @Value("${bloom-contact-cl-processor.url.update}")
    private String bloomContactClUrl;

    private List<QuoteDTO> callSearchQuotes(String creationDateFrom, String creationDateTo, String phoneNumber) {
        List<QuoteDTO> quoteDTOs;

        try {
            quoteDTOs = bloomRepositoryClient.searchQuotesByPhone(phoneNumber);

        } catch (Exception ex) {
            String errorMessage = String.format("Exception when calling bloom repository service searchQuotes with" +
                    " creationDateFrom = %s, creationDateTo = %s and phoneNumber = %s.", creationDateFrom, creationDateTo, phoneNumber);
            throw new BloomRepositoryServiceException(errorMessage, ex);
        }

        return quoteDTOs;
    }

    public List<QuoteDTO> searchQuotesByWorkFlowStatus(String workFlowStatus) {
        List<QuoteDTO> quoteDTOs;

        try {
            quoteDTOs = bloomRepositoryClient.searchQuotesByWorkFlowStatus(workFlowStatus);

        } catch (ApiException e) {
            String msg = "Exception when calling Search for the workFlowStatus : %s".formatted(workFlowStatus);
            throw new BloomRepositoryServiceException(msg, e);
        }

        return quoteDTOs;
    }

    public QuoteDTO searchByQuoteNumber(String quoteNumber) {

        QuoteDTO quoteDTO;

        try {
            quoteDTO = bloomRepositoryClient.searchQuotesByQuoteNumber(quoteNumber);
        } catch (ApiException e) {
            String msg = "Exception when calling Search for the quoteNumber : %s".formatted(quoteNumber);
            throw new BloomRepositoryServiceException(msg, e);
        }

        return quoteDTO;
    }

    public QuoteUIDTO partialUpdateQuote(QuoteUIDTO entryQuote){

        if(entryQuote == null) {
            throw  new IllegalArgumentException("The entryQuote parameter couldn't not be null");
        }

        QuoteDTO quoteDTO = new QuoteDTO();
        QuoteDTO updatedQuote;

        if(Boolean.TRUE.equals(entryQuote.getIsHidden())) {
            quoteDTO.setWorkFlowStatus(QuoteDTO.WorkFlowStatusEnum.DELETED);
        } else {
            quoteDTO.setWorkFlowStatus(QuoteDTO.WorkFlowStatusEnum.READY);
        }

        quoteDTO.setId(entryQuote.getId());

        try {

             updatedQuote = this.bloomRepositoryClient.partialUpdateQuote(quoteDTO);

        } catch (ApiException ex) {
            String msg = "Exception when calling partialUpdateQuote quotes with quoteNumber = %s".formatted(quoteDTO.getQuoteNumber());
            throw new BloomRepositoryServiceException(msg, ex);
        }

        QuoteDialerDTO quoteDialerDTO = quoteDTOToQuoteDialerDTOConverter.convert(updatedQuote);

        if( quoteDialerDTO != null) {
            quoteDialerDTO.setUserName(this.userAccount.getUserId());

            updateFollowUpMessage(quoteDialerDTO, updatedQuote.getWorkFlowStatus(),null);
        }

        return converter.convert(updatedQuote);
    }

    private List<QuoteUIDTO> convertToQuoteUIDTOs(List<QuoteDTO> quoteDTOs) {

        List<QuoteUIDTO> quoteUIDTOs = null;

        if(CollectionUtils.isNotEmpty(quoteDTOs)) {
            quoteUIDTOs = quoteDTOs.stream().map(result -> converter.convert(result)).collect(Collectors.toList());
            log.debug(Logger.EVENT_SUCCESS, "Search successful [%s results found]".formatted(quoteUIDTOs.size()));
        }

        return quoteUIDTOs;
    }

    /**
     * Search and return, for the specified phone number, all the quotes that should be reviewed before being sent to dialer.
     */
    public  List<QuoteUIDTO> getQuotesForReview(String phoneNumber) {
        if(StringUtils.isBlank(phoneNumber)) {
            return Collections.emptyList();
        }
        List<QuoteDTO> quotes = callSearchQuotes(null, null, phoneNumber).stream()
                .filter(quote -> quote.getWorkFlowStatus() == WorkFlowStatusEnum.READY)
                .filter(quote ->  quote.getCreationTimeStamp().isAfter(DateTime.now().minusMonths(2)))
                .collect(Collectors.toList());
        
        return convertToQuoteUIDTOs(quotes);
    }


    public PageUIDTO searchLeadListQuotes(String creationDateFrom, String creationDateTo, String province, Integer page, Integer size) throws ApiException {
        return pageToPageUIDTOConverter.convert(this.bloomRepositoryClient.searchLeadListQuotes(creationDateFrom,creationDateTo,province,page,size));
    }

    private void updateFollowUpMessage(QuoteDialerDTO quoteDialerToBeUpdated, QuoteDTO.WorkFlowStatusEnum workFlowStatusEnum, String dialerConfirmationNumber){
        try {
            //Update Follow Up Status for WebZone. Temporary Code. To be deleted
            FollowUpMessage followUpMessage = FollowUpMessageHelper.buildFollowUpMessage(quoteDialerToBeUpdated, workFlowStatusEnum,  dialerConfirmationNumber);
            restTemplate.postForObject(getUrl(quoteDialerToBeUpdated.getDatasourceOrigin()), followUpMessage, Boolean.class);

        } catch (Exception ex) {
            log.info(Logger.EVENT_FAILURE, "the quote with the number : %s was not updated".formatted(quoteDialerToBeUpdated.getQuoteNumber()),ex);
        }
    }

    private String getUrl(QuoteDialerDTO.DatasourceOriginEnum datasourceOrigin) {
        if (FollowUpMessageHelper.isAuto(datasourceOrigin))
            return bloomPlpUrl;
        else if (FollowUpMessageHelper.isHome(datasourceOrigin))
            return bloomXpasUrl;
        else
            return bloomContactClUrl;
    }
    
}
