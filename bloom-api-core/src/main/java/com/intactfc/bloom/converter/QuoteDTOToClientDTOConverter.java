package com.intactfc.bloom.converter;

import java.util.Objects;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.convert.converter.Converter;
import org.springframework.stereotype.Component;

import com.intactfc.bloom.facade.ClientDTO;

import intact.bloom.api.client.model.PartyDTO;
import intact.bloom.api.client.model.QuoteDTO;
import intact.bloom.api.client.model.PartyDTO.PartyTypeEnum;

@Component
public class QuoteDTOToClientDTOConverter implements Converter<QuoteDTO, ClientDTO>{
    
    @Autowired
    private PhoneDTOToStringConverter phoneConverter;
    
    @Override
    public ClientDTO convert(QuoteDTO quote) {
        ClientDTO client = new ClientDTO();
        client.setName(getFullName(quote));
        client.setFirstName(quote.getParty().getFirstName());
        client.setLastName(quote.getParty().getLastName());
        client.setCompanyName(quote.getParty().getCompanyName());
        String phoneNumber = phoneConverter.convert(quote.getPhone());
        client.setPhone(phoneNumber);
        client.setPostalCode(quote.getParty().getAddress().getPostalCode());
        client.setProvince(quote.getParty().getAddress().getProvince());
        return client;
    }
    
    private String getFullName(QuoteDTO quote) {
        PartyDTO party = quote.getParty();
        return Objects.toString(party.getFirstName(), "")+" "+Objects.toString(party.getLastName(), "");
    }
}
