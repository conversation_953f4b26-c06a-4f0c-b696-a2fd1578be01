package com.intact.dialer.service;

import intact.bloom.dialer.service.client.api.ApiException;
import intact.bloom.dialer.service.client.api.DialerControllerApi;
import intact.bloom.dialer.service.client.model.QuoteDialerDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class DialerService implements IDialerService {

    @Autowired
    private DialerControllerApi dialerControllerApi;

    public String send(QuoteDialerDTO quoteDialerDTO) throws ApiException {
        return dialerControllerApi.createDialerFileUsingPOST(quoteDialerDTO);

    }
}
