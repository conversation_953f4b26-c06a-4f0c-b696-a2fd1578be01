package com.intactfc.bloom.facade;

import com.intact.dialer.service.IDialerService;
import com.intactfc.bloom.converter.QuoteDTOToQuoteUIDTOConverter;
import com.intactfc.bloom.domain.DialerDTO;
import com.intactfc.bloom.domain.DialerResponseDTO;
import com.intactfc.bloom.domain.UserAccount;
import com.intactfc.bloom.exceptions.DialerServiceException;
import com.intactfc.bloom.exceptions.QuoteNotFoundException;
import com.intactfc.bloom.mock.quote.MockPhoneDTO;
import com.intactfc.bloom.mock.quote.MockQuoteDTO;
import com.intactfc.bloom.mock.quotedialer.MockQuoteDialerDTO;
import com.intactfc.bloom.service.BloomRepositoryClient;
import intact.bloom.api.client.model.QuoteDTO;
import intact.bloom.api.client.model.QuoteDTO.WorkFlowStatusEnum;
import intact.bloom.dialer.service.client.model.QuoteDialerDTO;

import org.joda.time.DateTime;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.springframework.core.convert.converter.Converter;

import java.util.LinkedList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class DialerFacadeTest {

    @InjectMocks
    private DialerFacade dialerFacade = new DialerFacade();

    @Mock
    private Converter<QuoteDTO, QuoteDialerDTO> quoteDTOToQuoteDialerDTOConverter;

    @Mock
    private IDialerService dialerService;

    @Mock
    private QuoteDTOToQuoteUIDTOConverter mockConverter;
    
    @Mock
    private UserAccount userAccount;

    private QuoteDialerDTO quoteDialerDTOMock;

    private List<QuoteDTO> mockQuotesList;

    private List<QuoteDTO> quotes;
    private QuoteDTO quoteDTO5;
    private QuoteDTO quoteDTO;
    private QuoteDTO quoteDTO1;
    private QuoteDTO quoteDTO2;
    private QuoteDTO quoteDTO3;

    @Mock
    private BloomRepositoryClient bloomRepositoryClient;

    private DialerDTO dialerDTO;

    @BeforeEach
    void setUp() {

        quoteDialerDTOMock = MockQuoteDialerDTO.createQuoteDialerDTO();
        quoteDTO = new QuoteDTO();
        quoteDTO.setCreationTimeStamp(DateTime.now());
        quoteDTO.setQuoteNumber("QF123456798");
        quoteDTO.setWorkFlowStatus(WorkFlowStatusEnum.READY);
        quoteDTO.setPhone(MockPhoneDTO.createPhoneDTO());
        quoteDTO1 = new QuoteDTO();
        quoteDTO1.setCreationTimeStamp(DateTime.now());
        quoteDTO1.setQuoteNumber("********-9999-9999-9999-********9999");
        quoteDTO1.setWorkFlowStatus(WorkFlowStatusEnum.READY);
        quoteDTO1.setPhone(MockPhoneDTO.createPhoneDTO());
        quoteDTO2 = new QuoteDTO();
        quoteDTO2.setCreationTimeStamp(DateTime.now());
        quoteDTO2.setQuoteNumber("QT100000");
        quoteDTO2.setWorkFlowStatus(WorkFlowStatusEnum.READY);
        quoteDTO3 = new QuoteDTO();
        quoteDTO3.setCreationTimeStamp(DateTime.now());
        quoteDTO3.setQuoteNumber("testQuote");
        quoteDTO3.setWorkFlowStatus(WorkFlowStatusEnum.READY);
        mockQuotesList = new LinkedList<QuoteDTO>();
        mockQuotesList.add(quoteDTO);
        mockQuotesList.add(quoteDTO1);
        mockQuotesList.add(quoteDTO2);
        mockQuotesList.add(quoteDTO3);

        quotes = new LinkedList<>();
        quoteDTO5 = MockQuoteDTO.createQuoteDTO();
        quotes.add(quoteDTO5);

        when(quoteDTOToQuoteDialerDTOConverter.convert(any(QuoteDTO.class))).thenReturn(quoteDialerDTOMock);
    }

    @Test
    void test_sendToDialer_sucess() throws Exception {
        when(bloomRepositoryClient.searchQuoteByUuid("12")).thenReturn(quotes.getFirst());
        when(bloomRepositoryClient.searchQuotesByPhone(quoteDTO5.getPhone().getAreaCode() + quoteDTO5.getPhone().getPhoneNumber())).thenReturn(quotes);

        dialerDTO = DialerDTO.builder().uuid("12").build();

        when(quoteDTOToQuoteDialerDTOConverter.convert(quotes.getFirst())).thenReturn(quoteDialerDTOMock);
        when(dialerService.send(quoteDialerDTOMock)).thenReturn("123456");

        DialerResponseDTO dialerResponseDTO = dialerFacade.sendToDialer(dialerDTO);

        assertNotNull(dialerResponseDTO);
        assertEquals("123456",dialerResponseDTO.getDialerConfirmationNumber());
    }

    @Test
    void test_sendToDialer_sucess_with_linked_and_deleted_quotes() throws Exception {

        when(bloomRepositoryClient.searchQuoteByUuid("12")).thenReturn(quotes.getFirst());
        when(bloomRepositoryClient.searchQuotesByPhone(anyString())).thenReturn(mockQuotesList);

        dialerDTO = DialerDTO.builder().uuid("12").linkedQuoteNumbers(new String[]{"********-9999-9999-9999-********9999","testQuote"}).deletedQuoteNumbers(new String[]{"QT100000"}).build();

        when(quoteDTOToQuoteDialerDTOConverter.convert(quotes.getFirst())).thenReturn(quoteDialerDTOMock);
        when(dialerService.send(quoteDialerDTOMock)).thenReturn("123456");

        DialerResponseDTO dialerResponseDTO = dialerFacade.sendToDialer(dialerDTO);

        assertNotNull(dialerResponseDTO);
        assertEquals("123456",dialerResponseDTO.getDialerConfirmationNumber());
    }

    @Test
    void test_sendToDialer_sucess_with_one_new_quotes_in_Database() throws Exception {

        // This quote is two months old : it should not be returned as a 'new quote' for reviewing.
        QuoteDTO outdatedQuote = new QuoteDTO();
        outdatedQuote.setCreationTimeStamp(DateTime.now().minusMonths(2));
        outdatedQuote.setQuoteNumber("outdatedQuote1");
        outdatedQuote.setWorkFlowStatus(WorkFlowStatusEnum.READY);
        mockQuotesList.add(outdatedQuote);

        // This quote is not in the READY state : it should therefore not be returned as a 'new quote' for reviewing.
        QuoteDTO deletedQuote = new QuoteDTO();
        outdatedQuote.setCreationTimeStamp(DateTime.now().minusMonths(2));
        outdatedQuote.setQuoteNumber("outdatedQuote1");
        outdatedQuote.setWorkFlowStatus(WorkFlowStatusEnum.READY);
        mockQuotesList.add(deletedQuote);

        when(bloomRepositoryClient.searchQuoteByUuid("12")).thenReturn(quotes.getFirst());
        when(bloomRepositoryClient.searchQuotesByPhone(quoteDTO5.getPhone().getAreaCode() + quoteDTO5.getPhone().getPhoneNumber())).thenReturn(mockQuotesList);

        dialerDTO = DialerDTO.builder().uuid("12").linkedQuoteNumbers(new String[]{"********-9999-9999-9999-********9999"}).deletedQuoteNumbers(new String[]{"QT100000"}).build();

        DialerResponseDTO dialerResponseDTO = dialerFacade.sendToDialer(dialerDTO);

        assertNotNull(dialerResponseDTO);
        assertNotNull(dialerResponseDTO.getNewLinkedQuotesUIDTO());
        assertEquals(1,dialerResponseDTO.getNewLinkedQuotesUIDTO().size());
        assertNull(dialerResponseDTO.getDialerConfirmationNumber());
    }

    /**
     * When dialer service returns -1 as confirmation number, the façade should throw a DialerServiceException.
     */
    @Test
    void test_sendToDialer_returns_minus_one() throws Exception {
        assertThrows(DialerServiceException.class,() -> {
            when(bloomRepositoryClient.searchQuoteByUuid("12")).thenReturn(quotes.getFirst());
            when(bloomRepositoryClient.searchQuotesByPhone(quoteDTO5.getPhone().getAreaCode() + quoteDTO5.getPhone().getPhoneNumber())).thenReturn(quotes);

            dialerDTO = DialerDTO.builder().uuid("12").build();

            when(quoteDTOToQuoteDialerDTOConverter.convert(quotes.getFirst())).thenReturn(quoteDialerDTOMock);
            when(dialerService.send(any(QuoteDialerDTO.class))).thenReturn("-1");

            dialerFacade.sendToDialer(dialerDTO);
        });
    }

    @Test
    void test_sendToDialer_returns_quote_not_found_exception() throws Exception {
        assertThrows(QuoteNotFoundException.class,() -> {
            when(bloomRepositoryClient.searchQuoteByUuid("12")).thenReturn(null);

            dialerDTO = DialerDTO.builder().uuid("12").build();

            dialerFacade.sendToDialer(dialerDTO);
        });
    }

}
