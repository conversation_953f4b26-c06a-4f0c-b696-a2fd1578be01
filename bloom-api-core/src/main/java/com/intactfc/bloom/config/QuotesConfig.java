package com.intactfc.bloom.config;


import com.intact.dialer.config.DialerConfiguration;
import intact.bloom.api.client.api.ApiClient;
import intact.bloom.api.client.api.BloomRepositoryServiceApi;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.web.client.RestTemplate;

@Configuration
@Import(DialerConfiguration.class)
public class QuotesConfig {

    @Value("${bloom-repository-service.url}")
    private String bloomRepositoryServiceURL;

    public ApiClient apiClient() {
        ApiClient apiClient = new ApiClient();
        apiClient.setBasePath(this.bloomRepositoryServiceURL);
        return apiClient;
    }

  @Bean
  BloomRepositoryServiceApi bloomRepositoryServiceApi() {
    BloomRepositoryServiceApi bloomRepositoryServiceApi = new BloomRepositoryServiceApi();
    bloomRepositoryServiceApi.setApiClient(apiClient());
    return bloomRepositoryServiceApi;
  }

  @Bean
  RestTemplate restTemplate() {
    return new RestTemplate();
  }
}
