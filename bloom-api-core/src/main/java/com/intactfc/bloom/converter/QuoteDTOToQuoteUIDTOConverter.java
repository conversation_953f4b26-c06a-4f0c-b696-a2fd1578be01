package com.intactfc.bloom.converter;

import com.intactfc.bloom.domain.QuoteUIDTO;
import com.intactfc.bloom.utils.DateUtils;
import intact.bloom.api.client.model.AddressDTO;
import intact.bloom.api.client.model.QuoteDTO;
import intact.bloom.api.client.model.VehicleDTO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.text.WordUtils;
import org.joda.time.DateTime;
import org.joda.time.DateTimeZone;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.convert.converter.Converter;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/*
 * Class defining conversion methods to convert objects from the Bloom Repository DTO model to objects from the
 * Bloom API model.
 */
@Component
public class QuoteDTOToQuoteUIDTOConverter implements Converter<QuoteDTO,QuoteUIDTO> {
    private static final String NAME_FORMAT_FOR_PERSON = "%s %s";
    private static final String NAME_FORMAT_FOR_COMPANY = "%s";
    private static final String DATE_TIME_FORMAT = "yyyy/MM/dd - HH'h'mm";

    @Value("${timeZone}")
    private String timeZone;

    @Autowired
    private PhoneDTOToStringConverter phoneConverter;

    @Override
    public QuoteUIDTO convert(QuoteDTO entryQuote) {

        QuoteUIDTO resultQuote = null;

        if (entryQuote != null) {

            String compagnyName = entryQuote.getParty() == null
                    ? StringUtils.EMPTY
                    : WordUtils.capitalizeFully(NAME_FORMAT_FOR_COMPANY.formatted(
                            StringUtils.defaultIfBlank(entryQuote.getParty().getCompanyName(), StringUtils.EMPTY)),
                    '-', ' ');

            String fullName = getFullName(entryQuote);

            String phoneNumber = entryQuote.getPhone() == null
                    ? StringUtils.EMPTY
                    : phoneConverter.convert(entryQuote.getPhone());

            String dateTimeString = entryQuote.getCreationTimeStamp() != null
                    ? DateUtils.getMinutesBetweenTwoDateTimes(entryQuote.getCreationTimeStamp().withZone(DateTimeZone.forID(timeZone)), DateTime.now().withZone(DateTimeZone.forID(timeZone)))
                    : null;

            resultQuote = QuoteUIDTO.builder()
                    .id(entryQuote.getId())
                    .quoteNumber(entryQuote.getQuoteNumber())
                    .name((entryQuote.getParty() != null && entryQuote.getParty().getPartyType() != null && StringUtils.equalsIgnoreCase("COMPANY", entryQuote.getParty().getPartyType().getValue()))
                            ? compagnyName
                            : fullName)
                    .date(dateTimeString)
                    .phoneNumber(phoneNumber)
                    .broker(entryQuote.getBroker())
                    .vehicles(getVehicles(entryQuote))
                    .address(getAddressDTO(entryQuote))
                    .creationDateTime(DateUtils.formatDateTimeToString(entryQuote.getCreationTimeStamp() != null ? entryQuote.getCreationTimeStamp().withZone(DateTimeZone.forID(timeZone)) : entryQuote.getCreationTimeStamp(), DATE_TIME_FORMAT))
                    .isHidden(isHiddenQuote(entryQuote.getWorkFlowStatus()))
                    .isQuoteAlReadyClient(QuoteDTO.QuoteStatusEnum.ALREADY_CLIENT == entryQuote.getQuoteStatus())
                    .isQuoteRoadBlock(QuoteDTO.QuoteStatusEnum.ROAD_BLOCK == entryQuote.getQuoteStatus())
                    .isQuoteIncomplete(QuoteDTO.QuoteStatusEnum.QUOTE_INCOMPLETE == entryQuote.getQuoteStatus())
                    .isQuoteComplete(isQuoteComplete(entryQuote.getQuoteStatus()))
                    .isQuoteToBeReviewed(entryQuote.isRelatedInd())
                    .workflowStatus(entryQuote.getWorkFlowStatus())
                    .build();
        }

        return resultQuote;
    }

    private Boolean isQuoteComplete(QuoteDTO.QuoteStatusEnum quoteStatus) {
        return QuoteDTO.QuoteStatusEnum.QUOTE_COMPLETE == quoteStatus
                || QuoteDTO.QuoteStatusEnum.IN_PROGRESS_READY_TO_BIND == quoteStatus
                || QuoteDTO.QuoteStatusEnum.IN_FORCE == quoteStatus;
    }

    private String getFullName(QuoteDTO entryQuote) {
        String fullName;
        if(entryQuote.getParty() == null) {
            fullName = "";
        } else {
            String firstname = Objects.toString(entryQuote.getParty().getFirstName(), "");
            String lastname = Objects.toString(entryQuote.getParty().getLastName(), "");
            fullName= WordUtils.capitalizeFully(NAME_FORMAT_FOR_PERSON.formatted(firstname, lastname), '-', ' ');
        }
        return fullName;
    }

    private List<VehicleDTO> getVehicles(QuoteDTO quoteDTO) {

        if (CollectionUtils.isEmpty(quoteDTO.getListVehiclesDTO())) {
            return Collections.emptyList();
        }

        return quoteDTO.getListVehiclesDTO().stream().map(vehicle ->
            new VehicleDTO().code(vehicle.getCode())
                                   .make(vehicle.getMake())
                                   .model(vehicle.getModel())
                                   .year(vehicle.getYear())
        ).collect(Collectors.toList());
    }

    private AddressDTO getAddressDTO(QuoteDTO quoteDTO){
        if (quoteDTO.getParty() == null
                || quoteDTO.getParty().getAddress() == null){
            return new AddressDTO();
        }

        return quoteDTO.getParty().getAddress();
    }

    private boolean isHiddenQuote(QuoteDTO.WorkFlowStatusEnum workFlowStatus) {
        return workFlowStatus != QuoteDTO.WorkFlowStatusEnum.READY;
    }
}
