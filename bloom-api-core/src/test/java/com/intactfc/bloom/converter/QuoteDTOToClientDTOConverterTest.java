package com.intactfc.bloom.converter;

import static org.junit.jupiter.api.Assertions.assertEquals;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.intactfc.bloom.facade.ClientDTO;
import com.intactfc.bloom.mock.quote.MockQuoteDTO;

import intact.bloom.api.client.model.PhoneDTO;
import intact.bloom.api.client.model.QuoteDTO;
import intact.bloom.api.client.model.PartyDTO.PartyTypeEnum;

@ExtendWith(MockitoExtension.class)
class QuoteDTOToClientDTOConverterTest {
    @Mock
    private PhoneDTOToStringConverter phoneConverter;
    
    @InjectMocks
    private QuoteDTOToClientDTOConverter converter;

    @Test
    void convert_person() {
        when(phoneConverter.convert(any(PhoneDTO.class))).thenReturn("************");
        QuoteDTO quote = MockQuoteDTO.createQuoteDTO();
        ClientDTO client = converter.convert(quote);
        assertEquals("Test Test",client.getName());
        assertEquals("H1H1H1",client.getPostalCode());
        assertEquals("QC",client.getProvince());
        verify(phoneConverter).convert(quote.getPhone());
    }
    
}
