package com.intactfc.bloom.utils;

import com.intactfc.bloom.domain.FollowUpMessage;
import com.intactfc.bloom.enums.AgreementFollowUpStatusEnum;
import intact.bloom.api.client.model.QuoteDTO;
import intact.bloom.dialer.service.client.model.QuoteDialerDTO;

public class FollowUpMessageHelper {

    private FollowUpMessageHelper() {}

    public static FollowUpMessage buildFollowUpMessage(QuoteDialerDTO quoteDialerDTO, QuoteDTO.WorkFlowStatusEnum workFlowStatusEnum, String dialerConfirmationNumber) {

        if (isAuto(quoteDialerDTO.getDatasourceOrigin())) {
            return FollowUpMessage.builder().parameter(quoteDialerDTO.getQuoteNumber())
                    .dialerConfirmationNumber(dialerConfirmationNumber)
                    .listRelatedQuotes(quoteDialerDTO.getListRelatedQuotes())
                    .userName(quoteDialerDTO.getUserName())
                    .agreementFollowUpStatusEnum(getAgreementFollowUpStatusEnum(workFlowStatusEnum))
                    .build();
        } else if (isHome(quoteDialerDTO.getDatasourceOrigin())) {
            return FollowUpMessage.builder().parameter(quoteDialerDTO.getId())
                    .dialerConfirmationNumber(dialerConfirmationNumber)
                    .listRelatedQuotes(quoteDialerDTO.getListRelatedQuotes())
                    .userName(quoteDialerDTO.getUserName())
                    .agreementFollowUpStatusEnum(getAgreementFollowUpStatusEnum(workFlowStatusEnum))
                    .build();
        } else {
            return FollowUpMessage.builder().parameter(quoteDialerDTO.getId())
                    .dialerConfirmationNumber(dialerConfirmationNumber)
                    .listRelatedQuotes(quoteDialerDTO.getListRelatedQuotes())
                    .userName(quoteDialerDTO.getUserName())
                    .agreementFollowUpStatusEnum(getAgreementFollowUpStatusEnum(workFlowStatusEnum))
                    .build();
        }
    }


    public static boolean isAuto(QuoteDialerDTO.DatasourceOriginEnum datasourceOrigin) {
        return datasourceOrigin == QuoteDialerDTO.DatasourceOriginEnum.PLP;
    }

    public static boolean isHome(QuoteDialerDTO.DatasourceOriginEnum datasourceOrigin) {
        return datasourceOrigin == QuoteDialerDTO.DatasourceOriginEnum.XPAS;
    }

    public static AgreementFollowUpStatusEnum getAgreementFollowUpStatusEnum(QuoteDTO.WorkFlowStatusEnum workFlowStatusEnum) {
        return switch (workFlowStatusEnum) {
            case READY -> AgreementFollowUpStatusEnum.NOT_CONT;
            case DELETED -> AgreementFollowUpStatusEnum.DISCARD;
            case SENT_DIALER, LINKED -> AgreementFollowUpStatusEnum.CONT_BUT_NO_FLW;
            default -> null;
        };
    }
}
