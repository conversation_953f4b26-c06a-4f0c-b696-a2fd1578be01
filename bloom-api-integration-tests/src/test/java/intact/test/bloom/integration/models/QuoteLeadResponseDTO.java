package intact.test.bloom.integration.models;

import intact.test.integration.models.dto.AbstractResponseDTO;
import intact.test.integration.models.dto.ErrorDTO;
import java.util.ArrayList;
import java.util.List;
import lombok.Data;

@Data
public class QuoteLeadResponseDTO extends AbstractResponseDTO<List<QuoteLeadDTO>, ErrorDTO> {
  private List<QuoteLeadDTO> mainQuotes = new ArrayList<>();
  private List<QuoteLeadDTO> linkedQuotes = new ArrayList<>();
  private List<QuoteLeadDTO> otherQuotes = new ArrayList<>();

    @Override
    public boolean hasNullData() {
      return this.getData() == null;
    }
}
